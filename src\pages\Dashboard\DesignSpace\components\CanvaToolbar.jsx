import { useDesignSpace } from '@contexts/DesignSpaceContext';
import PropTypes from 'prop-types';
import { useEffect } from 'react';

// Icons
import { FiRotateCcw, FiRotateCw, FiLock, FiUnlock, FiSave, FiMinus, FiPlus, FiMaximize, FiCopy } from 'react-icons/fi';
import {
    BiSolidLayerPlus,
    BiSolidLayerMinus,
    BiGroup,
    BiLayer
} from 'react-icons/bi';

const CanvaToolbar = ({ saveDesign<PERSON>and<PERSON>, saveAsHandler, isDirty = true, isCreateMode = false }) => {
    const {
        selectedIds,
        undo,
        redo,
        toggleLock,
        groupElements,
        ungroupElements,
        zoomLevel,
        zoom,
        bringToFront,
        sendToBack,
        setSelectedIds,
        cardType, // <-- add cardType
        elements  // <-- add elements
    } = useDesignSpace();

    // إضافة اختصارات لوحة المفاتيح للتكبير والتصغير
    useEffect(() => {
        const handleKeyDown = (e) => {
            // تجاهل إذا كان التركيز على input أو textarea
            const active = document.activeElement;
            if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
                return;
            }

            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '=':
                    case '+':
                        e.preventDefault();
                        handleZoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        handleZoomOut();
                        break;
                    case '0':
                        e.preventDefault();
                        handleZoomReset();
                        break;
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [zoomLevel]);

    const handleLockToggle = () => {
        if (selectedIds.length > 0) {
            toggleLock(selectedIds);
        }
    };

    const handleGroup = () => {
        if (selectedIds.length > 1) {
            groupElements(selectedIds);
        }
    };

    const handleUngroup = () => {
        if (selectedIds.length === 1) {
            const groupId = selectedIds[0];
            if (groupId.startsWith('group_')) {
                ungroupElements(groupId);
            }
        }
    };

    // دوال التحكم في الزوم
    const handleZoomIn = () => {
        const newZoomLevel = Math.min(zoomLevel + 10, 200);
        if (zoomLevel !== newZoomLevel) {
            zoom(newZoomLevel);
        }
    };
    
    const handleZoomOut = () => {
        const newZoomLevel = Math.max(zoomLevel - 10, 50);
        if (zoomLevel !== newZoomLevel) {
            zoom(newZoomLevel);
        }
    };
    
    const handleZoomReset = () => {
        if (zoomLevel !== 100) {
            zoom(100);
        }
    };
    // دالة قفل العنصر
    const isElementLocked = () => {
        // إذا كان لديك منطق للقفل، ضعه هنا. مؤقتاً يرجع false دائماً
        return false;
    };

    // Disable Save As New if no cardType or elements is empty
    const isSaveAsDisabled = !cardType || !cardType.id || !elements || elements.length === 0;

    return (
        <div className="canva-toolbar w-full bg-white border-b border-gray-200 flex items-center justify-between px-0 py-2">
            <div className="flex items-center space-x-4">
                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Undo/Redo */}
                <div className="flex items-center space-x-2">
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={undo}
                        title="Undo"
                    >
                        <FiRotateCcw />
                    </button>
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={redo}
                        title="Redo"
                    >
                        <FiRotateCw />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Zoom Controls */}
                <div className="flex items-center space-x-2 zoom-controls">
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomOut();
                        }}
                        className={`p-2 hover:bg-gray-100 rounded transition-colors ${zoomLevel <= 50 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title="Zoom Out (Ctrl + -)"
                        disabled={zoomLevel <= 50}
                    >
                        <FiMinus />
                    </button>
                    <span className="text-sm font-medium zoom-level-display">
                        {zoomLevel}%
                    </span>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomIn();
                        }}
                        className={`p-2 hover:bg-gray-100 rounded transition-colors ${zoomLevel >= 200 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title="Zoom In (Ctrl + +)"
                        disabled={zoomLevel >= 200}
                    >
                        <FiPlus />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomReset();
                        }}
                        className={`p-2 hover:bg-gray-100 rounded transition-colors ${zoomLevel === 100 ? 'bg-blue-100 text-blue-600' : ''}`}
                        title="Reset Zoom (Ctrl + 0)"
                    >
                        <FiMaximize />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Element Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleLockToggle}
                        disabled={selectedIds.length === 0}
                        title={selectedIds.length > 0 && isElementLocked() ? "Unlock" : "Lock"}
                    >
                        {selectedIds.length > 0 && isElementLocked() ? <FiUnlock /> : <FiLock />}
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleGroup}
                        disabled={selectedIds.length < 2}
                        title="Group"
                    >
                        <BiGroup />
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 || !selectedIds[0].startsWith('group_') ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleUngroup}
                        disabled={selectedIds.length !== 1 || !selectedIds[0].startsWith('group_')}
                        title="Ungroup"
                    >
                        <BiLayer />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Layer Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && bringToFront(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Bring to Front"
                    >
                        <BiSolidLayerPlus />
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && sendToBack(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Send to Back"
                    >
                        <BiSolidLayerMinus />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Image Editing Tools */}
               
            </div>

            {/* Save Design Button */}
            <div className="flex items-center">
                {!isCreateMode && (
                    <button
                        className={`main-btn text-md shadow-sm flex items-center ${!isDirty ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={isDirty ? (() => {
                            setSelectedIds([]);
                            setTimeout(() => {
                                if (saveDesignHandler) saveDesignHandler();
                            }, 0);
                        }) : undefined}
                        style={{
                            backgroundColor: !isDirty ? '#bdbdbd' : 'var(--main_color, #00c3ac)',
                            borderColor: !isDirty ? '#bdbdbd' : 'var(--main_color, #00c3ac)',
                            fontWeight: 'bold',
                            borderRadius: '6px',
                            padding: '6px 10px',
                            color: '#ffffff',
                            transition: 'background 0.2s, border 0.2s',
                            marginRight: '12px'
                        }}
                        disabled={!isDirty}
                        title={!isDirty ? 'No changes to save' : 'Save Design'}
                    >
                        <FiSave className="mr-2" style={{ color: '#ffffff' }} />
                        <span style={{ color: '#ffffff' }}>Save Design</span>
                    </button>
                )}
                <button
                    className={`main-btn text-md shadow-sm flex items-center ${isSaveAsDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => {
                        if (isSaveAsDisabled) return;
                        setSelectedIds([]);
                        setTimeout(() => {
                            if (saveAsHandler) saveAsHandler();
                        }, 0);
                    }}
                    style={{
                        backgroundColor: isSaveAsDisabled ? '#bdbdbd' : 'var(--main_color, #6c63ff)',
                        borderColor: isSaveAsDisabled ? '#bdbdbd' : 'var(--main_color, #6c63ff)',
                        fontWeight: 'bold',
                        borderRadius: '6px',
                        padding: '6px 10px',
                        marginRight:'20px',
                        color: '#ffffff',
                        transition: 'background 0.2s, border 0.2s',
                    }}
                    title={isSaveAsDisabled ? 'يجب اختيار نوع البطاقة ووجود عناصر في منطقة التصميم' : 'Save As Design'}
                    disabled={isSaveAsDisabled}
                >
                    <FiCopy className="mr-2" style={{ color: '#ffffff' }} />
                    <span style={{ color: '#ffffff' }}>Save As New</span>
                </button>
            </div>
        </div>
    );
};

CanvaToolbar.propTypes = {
    saveDesignHandler: PropTypes.func.isRequired,
    saveAsHandler: PropTypes.func.isRequired,
    isDirty: PropTypes.bool,
    isCreateMode: PropTypes.bool
};

export default CanvaToolbar;
