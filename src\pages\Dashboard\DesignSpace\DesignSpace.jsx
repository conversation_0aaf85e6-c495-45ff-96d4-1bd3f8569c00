import { useState, useEffect, useRef } from "react";
import PropTypes from 'prop-types';

import { useDesignSpace } from "@contexts/DesignSpaceContext";
import "./DesignSpaceOverrides.css"; // Import custom CSS to override transparency
import "./RotationHandles.css"; // Import custom CSS for rotation handles
import "./cursors.css"; // Import professional cursors
import "./MobileDesignSpace.css"; // Import mobile-specific styles

import AlignmentContainer from "./components/AlignmentContainer";
import AlignmentControl from "./components/AlignmentControl";
import DuplicateControl from "./components/DuplicateControl";
import DeleteControl from "./components/DeleteControl";
import ResizeInputs from "./components/ResizeInputs";
import TypeControl from "./components/TypeControl";
import Element from "./components/Element";
import CanvaToolbar from "./components/CanvaToolbar";
import DesignSpaceBackground from "./components/DesignSpaceBackground";
import LeftSidebar from "./components/LeftSidebar";
import { FiHelpCircle, FiChevronLeft, FiCrop } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { MdOutlineColorLens } from 'react-icons/md';
import { FaRegStar } from 'react-icons/fa';
import { useGlobalContext } from '@contexts/GlobalContext';
import ColorPicker from "./components/ColorPicker";
import { useUpdateTemplateMutation } from '@quires';

// Helper to measure text size for a given font
function measureText(text, fontSize, fontFamily, fontWeight, fontStyle, lineHeight, maxWidth = null) {
    // Create a hidden span for measurement
    const span = document.createElement('span');
    span.style.position = 'absolute';
    span.style.visibility = 'hidden';
    span.style.whiteSpace = 'pre-wrap';
    span.style.fontSize = fontSize + 'px';
    span.style.fontFamily = fontFamily;
    span.style.fontWeight = fontWeight;
    span.style.fontStyle = fontStyle;
    span.style.lineHeight = lineHeight;
    span.style.padding = '4px';
    span.style.letterSpacing = 'normal';
    span.style.textTransform = 'none';
    span.style.textDecoration = 'none';
    span.style.width = maxWidth ? maxWidth + 'px' : 'auto';
    span.innerText = text || 'Text';
    document.body.appendChild(span);
    const rect = span.getBoundingClientRect();
    document.body.removeChild(span);
    return { width: rect.width, height: rect.height };
}

// دالة مساعدة لضبط الأبعاد داخل حدود التصميم (نفس منطق TextSettings)
function fitTextElementWithinBounds(x, y, width, height, cardType) {
  let newWidth = width;
  let newHeight = height;
  let newX = x;
  let newY = y;
  if (cardType) {
    if (newX < 0) {
      newWidth = Math.max(40, newWidth + newX);
      newX = 0;
    }
    if (newY < 0) {
      newHeight = Math.max(24, newHeight + newY);
      newY = 0;
    }
    if (newX + newWidth > cardType.width) {
      newWidth = Math.max(40, cardType.width - newX);
    }
    if (newY + newHeight > cardType.height) {
      newHeight = Math.max(24, cardType.height - newY);
    }
  }
  return { x: newX, y: newY, width: newWidth, height: newHeight };
}

const DesignSpace = ({ updateTemplateData, design, onImageGenerationStart, onSaveAsDesign }) => {
    const [alignmentLines, setAlignmentLines] = useState({ vertical: null, horizontal: null });
    const [toolbarPosition, setToolbarPosition] = useState(null);
    const [toolbarClasses, setToolbarClasses] = useState('');

    // Mobile responsiveness states
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
    const [showMobileHint, setShowMobileHint] = useState(false);
    const hintTimeoutRef = useRef(null);

    const {
        selectedIds, setSelectedIds,
        updateElement, designSpaceRef,
        elements, setElements, isMultiSelectActive,
        setSelectedElement, cardType,
        canvasBackgroundStyle,
        bringToFront, sendToBack,
        zoomLevel,
        saveTextStyle,
        zoom
    } = useDesignSpace();

    const clipboardRef = useRef(null);

    const { dialogHandler } = useGlobalContext();

    const [pendingSave, setPendingSave] = useState(false);

    const [draggingElementId, setDraggingElementId] = useState(null);
    // refs for drag threshold
    const dragStartX = useRef(0);
    const dragStartY = useRef(0);
    const isActuallyDragging = useRef(false);

    const [colorPickerTargetId, setColorPickerTargetId] = useState(null);

    // بعد useState الأخرى
    const [activeCrop, setActiveCrop] = useState({ elementId: null, side: null });
    const [activeResize, setActiveResize] = useState({ elementId: null, corner: null });
    const [activeTextFrameResize, setActiveTextFrameResize] = useState({ elementId: null, side: null });
  
    const [shouldOpenCropTab, setShouldOpenCropTab] = useState(false);

    const [initialElements, setInitialElements] = useState([]);
    const [initialBackgroundStyle, setInitialBackgroundStyle] = useState(null);
    const [isDirty, setIsDirty] = useState(false);

    const updateTemplate = useUpdateTemplateMutation();

    // Mobile detection useEffect
    useEffect(() => {
        const handleResize = () => {
            const mobileView = window.innerWidth < 768;
            setIsMobile(mobileView);

            // Add mobile class to body for better mobile styling
            if (mobileView) {
                document.body.classList.add('mobile-design-space');
            } else {
                document.body.classList.remove('mobile-design-space');
            }
        };

        // Initial check
        handleResize();

        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
            document.body.classList.remove('mobile-design-space');
            if (hintTimeoutRef.current) {
                clearTimeout(hintTimeoutRef.current);
            }
        };
    }, []);

    // Mobile touch gesture support
    useEffect(() => {
        if (!isMobile) return;

        let touchStartX = 0;
        let touchStartY = 0;
        let touchStartTime = 0;
        let isLongPress = false;
        let longPressTimer = null;

        const handleTouchStart = (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            touchStartTime = Date.now();
            isLongPress = false;

            // Set up long press detection
            longPressTimer = setTimeout(() => {
                isLongPress = true;
                // Trigger haptic feedback if available
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            }, 500);
        };

        const handleTouchMove = () => {
            // Cancel long press if user moves finger
            if (longPressTimer) {
                clearTimeout(longPressTimer);
                longPressTimer = null;
            }
        };

        const handleTouchEnd = (e) => {
            if (longPressTimer) {
                clearTimeout(longPressTimer);
                longPressTimer = null;
            }

            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const touchEndTime = Date.now();

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const deltaTime = touchEndTime - touchStartTime;

            // Detect swipe gestures (only if not a long press)
            if (!isLongPress && Math.abs(deltaX) > 50 && deltaTime < 300) {
                if (deltaX > 0) {
                    // Swipe right - could be used for navigation
                    console.log('Swipe right detected');
                } else {
                    // Swipe left - could be used for navigation
                    console.log('Swipe left detected');
                }
            }

            if (!isLongPress && Math.abs(deltaY) > 50 && deltaTime < 300) {
                if (deltaY > 0) {
                    // Swipe down - could close mobile panels
                    console.log('Swipe down detected');
                } else {
                    // Swipe up - could open mobile panels
                    console.log('Swipe up detected');
                }
            }
        };

        const designSpace = designSpaceRef.current;
        if (designSpace) {
            designSpace.addEventListener('touchstart', handleTouchStart, { passive: true });
            designSpace.addEventListener('touchmove', handleTouchMove, { passive: true });
            designSpace.addEventListener('touchend', handleTouchEnd, { passive: true });

            return () => {
                if (longPressTimer) {
                    clearTimeout(longPressTimer);
                }
                designSpace.removeEventListener('touchstart', handleTouchStart);
                designSpace.removeEventListener('touchmove', handleTouchMove);
                designSpace.removeEventListener('touchend', handleTouchEnd);
            };
        }
    }, [isMobile, designSpaceRef]);

    // Helper function to get mouse position relative to design space, considering zoom
    const getRelativeMousePosition = (e, designSpaceRect, zoomLevel) => {
        // zoomLevel is a percentage (e.g., 100, 120, 80)
        const scale = zoomLevel / 100;
        const x = (e.clientX - designSpaceRect.left) / scale;
        const y = (e.clientY - designSpaceRect.top) / scale;
        return { x, y };
    };

    // دالة مساعدة لحساب موضع شريط الأدوات
    const updateToolbarForElement = (element, overrideRect = null) => {
        // Get the design space container
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        // استخدم القيم الجديدة إذا تم تمريرها
        let elementRect;
        if (overrideRect) {
            // أنشئ rect افتراضي بناءً على القيم الجديدة
            elementRect = {
                left: overrideRect.x + designSpaceRect.left,
                top: overrideRect.y + designSpaceRect.top,
                right: overrideRect.x + designSpaceRect.left + overrideRect.width,
                bottom: overrideRect.y + designSpaceRect.top + overrideRect.height,
                width: overrideRect.width,
                height: overrideRect.height
            };
        } else {
            // Get the DOM element
            const domElement = document.querySelector(`[data-element-id="${element.id}"]`);
            if (!domElement) return;
            elementRect = domElement.getBoundingClientRect();
        }
        const elementTop = elementRect.top - designSpaceRect.top;
        const elementRight = designSpaceRect.right - elementRect.right;
        const elementLeft = elementRect.left - designSpaceRect.left;
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;
        const edgeThreshold = 50;
        const toolbarDistance = 60;
        const isNearTop = elementTop < edgeThreshold;
        const isNearRight = elementRight < edgeThreshold;
        const isNearLeft = elementLeft < edgeThreshold;
        // تقدير عرض الشريط (يمكنك تعديله حسب التصميم الفعلي)
        const toolbarWidth = 220; // تقدير تقريبي
        // تحقق إذا كان الشريط سيخرج يميناً أو يساراً
        const willOverflowRight = (elementRect.right + toolbarWidth > designSpaceRect.right - 8);
        const willOverflowLeft = (elementRect.left - toolbarWidth < designSpaceRect.left + 8);
        let position = {
            position: 'absolute',
            display: 'flex',
            zIndex: 1000,
            pointerEvents: 'auto'
        };
        if ((isNearRight && willOverflowRight) || (isNearLeft && willOverflowLeft)) {
            // ضع الشريط فوق العنصر
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        } else if (isNearRight) {
            position = {
                ...position,
                left: `-${toolbarDistance}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else if (isNearLeft) {
            position = {
                ...position,
                left: `${elementWidth + 20}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else {
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
        if (isNearTop && !isNearRight && !isNearLeft) {
            position = {
                ...position,
                top: 'auto',
                bottom: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
        setToolbarPosition(position);
        let classes = '';
        if (isNearTop) classes += ' top-edge';
        if (isNearRight) classes += ' right-edge';
        if (isNearLeft) classes += ' left-edge';
        setToolbarClasses(classes.trim());
    };

    const handleMouseDown = (e, id, resizeCorner = null) => {
        if (e.target.closest('.element-controls')) return;
        e.stopPropagation();
        const isTouch = e.touches && e.touches.length > 0;
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;
        const { x: startX, y: startY } = getRelativeMousePosition(
            isTouch ? { clientX: e.touches[0].clientX, clientY: e.touches[0].clientY } : e,
            designSpaceRect,
            zoomLevel
        );
        dragStartX.current = isTouch ? e.touches[0].clientX : e.clientX;
        dragStartY.current = isTouch ? e.touches[0].clientY : e.clientY;
        isActuallyDragging.current = false;
        let rafId = null;
        let lastTouchX = dragStartX.current;
        let lastTouchY = dragStartY.current;
        let lastDeltaX = 0;
        let lastDeltaY = 0;
        let isResizing = resizeCorner !== null;
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        const element = { ...elements[elementIndex] };
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const initialHeight = element.height;
        let anchorX = initialX;
        let anchorY = initialY;
        if (isResizing) {
            if (resizeCorner === 'top-left') {
                anchorX = initialX + initialWidth;
                anchorY = initialY + initialHeight;
            } else if (resizeCorner === 'top-right') {
                anchorX = initialX;
                anchorY = initialY + initialHeight;
            } else if (resizeCorner === 'bottom-left') {
                anchorX = initialX + initialWidth;
                anchorY = initialY;
            } else if (resizeCorner === 'bottom-right') {
                anchorX = initialX;
                anchorY = initialY;
            }
        }
        let paddingWidth = 0;
        let paddingHeight = 0;
        if (isResizing && (element.type === 'text' || element.type === 'label') && (resizeCorner === 'top-left' || resizeCorner === 'top-right' || resizeCorner === 'bottom-left' || resizeCorner === 'bottom-right')) {
            const { width: textWidth, height: textHeight } = measureText(
                element.value,
                element.fontSize || 16,
                element.fontFamily || 'Arial, sans-serif',
                element.fontWeight || 'normal',
                element.fontStyle || 'normal',
                element.lineHeight || 1.2
            );
            paddingWidth = (element.width || textWidth) - textWidth;
            paddingHeight = (element.height || textHeight) - textHeight;
        }
        const domElement = document.querySelector(`[data-element-id="${element.id}"]`);
        setDraggingElementId(id);
        const updateElementPosition = (touchX, touchY) => {
            console.log('updateElementPosition', touchX, touchY);
            // إذا كان touchX/touchY undefined، استخدم dragStartX/Y.current (لـ mouse)
            const clientX = typeof touchX === 'number' ? touchX : dragStartX.current;
            const clientY = typeof touchY === 'number' ? touchY : dragStartY.current;
            // ... استبدل كل استخدام dragStartX.current وdragStartY.current بـ clientX/clientY في حسابات السحب ...
            if (isResizing) {
                if (element.type === 'text' || element.type === 'label') {
                    if (resizeCorner === 'left' || resizeCorner === 'right') {
                        const mouseX = clientX - designSpaceRect.left;
                        let newWidth;
                        if (resizeCorner === 'left') {
                            newWidth = Math.max(40, initialWidth + (initialX - mouseX));
                            // لا تغير x إلا إذا كان left
                            let newX = initialX + (initialWidth - newWidth);
                            // تعديل: لا تسمح بالخروج عن الحد الأيسر مع هامش 20px
                            const LEFT_MARGIN = 20;
                            if (newX < LEFT_MARGIN) {
                                newWidth = initialWidth + (initialX - LEFT_MARGIN);
                                newX = LEFT_MARGIN;
                            }
                            setElements(prev => prev.map((el, idx) => {
                                if (idx !== elementIndex) return el;
                                return { ...el, width: newWidth, x: newX };
                            }));
                            // تحديث مباشر في DOM (اختياري)
                            if (domElement) {
                                domElement.style.width = `${newWidth}px`;
                                domElement.style.left = `${newX}px`;
                                domElement.style.transition = 'none';
                            }
                            lastDeltaX = 0;
                            lastDeltaY = 0;
                        } else {
                            newWidth = Math.max(40, mouseX - initialX);
                            // لا تغير x إلا إذا كان left
                            let newX = initialX;
                            if (resizeCorner === 'left') {
                                newX = initialX + (initialWidth - newWidth);
                            }
                            setElements(prev => prev.map((el, idx) => {
                                if (idx !== elementIndex) return el;
                                return { ...el, width: newWidth, x: newX };
                            }));
                            // تحديث مباشر في DOM (اختياري)
                            if (domElement) {
                                domElement.style.width = `${newWidth}px`;
                                domElement.style.left = `${newX}px`;
                                domElement.style.transition = 'none';
                            }
                            lastDeltaX = 0;
                            lastDeltaY = 0;
                        }
                    } else {
                        // تغيير fontSize عند السحب من الزوايا
                        const mouseX = clientX - designSpaceRect.left;
                        const mouseY = clientY - designSpaceRect.top;
                        let dist = Math.sqrt(Math.pow(mouseX - anchorX, 2) + Math.pow(mouseY - anchorY, 2));
                        let initialDist = Math.sqrt(Math.pow(startX - anchorX, 2) + Math.pow(startY - anchorY, 2));
                        let newFontSize = Math.max(8, Math.round((element.fontSize || 16) * (dist / (initialDist || 1))));
                        const { width: newTextWidth, height: newTextHeight } = measureText(
                            element.value,
                            newFontSize,
                            element.fontFamily || 'Arial, sans-serif',
                            element.fontWeight || 'normal',
                            element.fontStyle || 'normal',
                            element.lineHeight || 1.2
                        );
                        const minWidth = 40;
                        const minHeight = 24;
                        const MARGIN = 8;
                        // أضف نفس الفرق (padding) للأبعاد الجديدة
                        const newWidthRaw = Math.max(newTextWidth + paddingWidth, minWidth);
                        const newHeightRaw = Math.max(newTextHeight + paddingHeight, minHeight);
                        // حساب newX و newY حسب الزاوية
                        let newX, newY;
                        if (resizeCorner === 'top-left') {
                            newX = anchorX - newWidthRaw;
                            newY = anchorY - newHeightRaw;
                        } else if (resizeCorner === 'top-right') {
                            newX = anchorX;
                            newY = anchorY - newHeightRaw;
                        } else if (resizeCorner === 'bottom-left') {
                            newX = anchorX - newWidthRaw;
                            newY = anchorY;
                        } else if (resizeCorner === 'bottom-right') {
                            newX = anchorX;
                            newY = anchorY;
                        }
                        let newWidth = newWidthRaw;
                        let newHeight = newHeightRaw;
                        // منع الخروج عن حدود التصميم مع الهامش
                        const canvasWidth = designSpaceRect.width / scale;
                        const canvasHeight = designSpaceRect.height / scale;
                        // ضبط newX و newY و newWidth و newHeight
                        let hitBoundary = false;
                        if (newX < MARGIN) {
                            newWidth -= (MARGIN - newX);
                            newX = MARGIN;
                            hitBoundary = true;
                        }
                        if (newY < MARGIN) {
                            newHeight -= (MARGIN - newY);
                            newY = MARGIN;
                            hitBoundary = true;
                        }
                        if (newX + newWidth > canvasWidth - MARGIN) {
                            newWidth = canvasWidth - MARGIN - newX;
                            hitBoundary = true;
                        }
                        if (newY + newHeight > canvasHeight - MARGIN) {
                            newHeight = canvasHeight - MARGIN - newY;
                            hitBoundary = true;
                        }
                        let adjustedFontSize = newFontSize;
                        if (hitBoundary) {
                            let measured = measureText(
                                element.value,
                                adjustedFontSize,
                                element.fontFamily || 'Arial, sans-serif',
                                element.fontWeight || 'normal',
                                element.fontStyle || 'normal',
                                element.lineHeight || 1.2
                            );
                            while ((measured.width + paddingWidth > newWidth || measured.height + paddingHeight > newHeight) && adjustedFontSize > 8) {
                                adjustedFontSize -= 1;
                                measured = measureText(
                                    element.value,
                                    adjustedFontSize,
                                    element.fontFamily || 'Arial, sans-serif',
                                    element.fontWeight || 'normal',
                                    element.fontStyle || 'normal',
                                    element.lineHeight || 1.2
                                );
                            }
                        }
                        setElements(prev => prev.map((el, idx) => {
                            if (idx !== elementIndex) return el;
                            return { ...el, fontSize: adjustedFontSize, width: newWidth, height: newHeight, x: newX, y: newY };
                        }));
                        if (domElement) {
                            domElement.style.fontSize = `${adjustedFontSize}px`;
                            domElement.style.width = `${newWidth}px`;
                            domElement.style.height = `${newHeight}px`;
                            domElement.style.left = `${newX}px`;
                            domElement.style.top = `${newY}px`;
                            domElement.style.transition = 'none';
                        }
                        lastDeltaX = 0;
                        lastDeltaY = 0;
                    }
                } else if (element.type === 'img') {
                    // إذا كان العنصر صورة، غير الأبعاد بشكل متساوي فقط (حافظ على النسبة)
                    const mouseX = clientX - designSpaceRect.left;
                    const mouseY = clientY - designSpaceRect.top;
                    let distX = Math.abs(mouseX - anchorX);
                    let distY = Math.abs(mouseY - anchorY);
                    const aspectRatio = initialWidth / initialHeight;
                    let sizeW = distX;
                    let sizeH = distY;
                    if (sizeW / aspectRatio > sizeH) {
                        sizeW = sizeH * aspectRatio;
                    } else {
                        sizeH = sizeW / aspectRatio;
                    }
                    let newWidth = Math.max(20, sizeW);
                    let newHeight = Math.max(20, sizeH);
                    // اضبط x وy حسب الزاوية المتحركة
                    let newX = initialX;
                    let newY = initialY;
                    if (resizeCorner === 'top-left') {
                        newX = anchorX - newWidth;
                        newY = anchorY - newHeight;
                    } else if (resizeCorner === 'top-right') {
                        newX = anchorX;
                        newY = anchorY - newHeight;
                    } else if (resizeCorner === 'bottom-left') {
                        newX = anchorX - newWidth;
                        newY = anchorY;
                    } else if (resizeCorner === 'bottom-right') {
                        newX = anchorX;
                        newY = anchorY;
                    }
                    // لا تخرج خارج حدود التصميم
                    newX = Math.max(0, Math.min(newX, designSpaceRect.width / scale - newWidth));
                    newY = Math.max(0, Math.min(newY, designSpaceRect.height / scale - newHeight));
                    newWidth = Math.min(newWidth, designSpaceRect.width / scale - newX);
                    newHeight = Math.min(newHeight, designSpaceRect.height / scale - newY);
                    // تحديث مباشر في DOM (اختياري)
                    if (domElement) {
                        domElement.style.left = `${newX}px`;
                        domElement.style.top = `${newY}px`;
                        domElement.style.width = `${newWidth}px`;
                        domElement.style.height = `${newHeight}px`;
                        domElement.style.transition = 'none';
                    }
                    lastDeltaX = newX - initialX;
                    lastDeltaY = newY - initialY;
                    setElements(prev => prev.map((el, idx) => {
                        if (idx !== elementIndex) return el;
                        return { ...el, x: newX, y: newY, width: newWidth, height: newHeight };
                    }));
                } else {
                    // تغيير الحجم العادي للعناصر الأخرى
                    const mouseX = clientX - designSpaceRect.left;
                    const mouseY = clientY - designSpaceRect.top;
                    let newWidth = initialWidth;
                    let newHeight = initialHeight;
                    let newX = initialX;
                    let newY = initialY;
                    if (resizeCorner === 'top-left') {
                        newX = Math.min(mouseX, anchorX - 20);
                        newY = Math.min(mouseY, anchorY - 20);
                        newWidth = anchorX - newX;
                        newHeight = anchorY - newY;
                    } else if (resizeCorner === 'top-right') {
                        newX = anchorX;
                        newY = Math.min(mouseY, anchorY - 20);
                        newWidth = Math.max(20, mouseX - anchorX);
                        newHeight = anchorY - newY;
                    } else if (resizeCorner === 'bottom-left') {
                        newX = Math.min(mouseX, anchorX - 20);
                        newY = anchorY;
                        newWidth = anchorX - newX;
                        newHeight = Math.max(20, mouseY - anchorY);
                    } else if (resizeCorner === 'bottom-right') {
                        newX = anchorX;
                        newY = anchorY;
                        newWidth = Math.max(20, mouseX - anchorX);
                        newHeight = Math.max(20, mouseY - anchorY);
                    }
                    // لا تخرج خارج حدود التصميم
                    newX = Math.max(0, Math.min(newX, designSpaceRect.width / scale - newWidth));
                    newY = Math.max(0, Math.min(newY, designSpaceRect.height / scale - newHeight));
                    newWidth = Math.min(newWidth, designSpaceRect.width / scale - newX);
                    newHeight = Math.min(newHeight, designSpaceRect.height / scale - newY);
                    // تحديث مباشر في DOM (اختياري)
                    if (domElement) {
                        domElement.style.left = `${newX}px`;
                        domElement.style.top = `${newY}px`;
                        domElement.style.width = `${newWidth}px`;
                        domElement.style.height = `${newHeight}px`;
                        domElement.style.transition = 'none';
                    }
                    lastDeltaX = newX - initialX;
                    lastDeltaY = newY - initialY;
                    setElements(prev => prev.map((el, idx) => {
                        if (idx !== elementIndex) return el;
                        return { ...el, x: newX, y: newY, width: newWidth, height: newHeight };
                    }));
                }
            } else {
                if (element.type === 'img' && !isActuallyDragging.current) {
                    return;
                }
                const { x: currentX, y: currentY } = getRelativeMousePosition({ clientX: dragStartX.current, clientY: dragStartY.current }, designSpaceRect, zoomLevel);
                lastDeltaX = currentX - startX;
                lastDeltaY = currentY - startY;
                const tempX = Math.max(0, Math.min(initialX + lastDeltaX, designSpaceRect.width / scale - element.width));
                const tempY = Math.max(0, Math.min(initialY + lastDeltaY, designSpaceRect.height / scale - element.height));
                if (domElement) {
                    domElement.style.left = `${tempX}px`;
                    domElement.style.top = `${tempY}px`;
                    domElement.style.transition = 'none';
                }
                showAlignmentLines(tempX, tempY, element, designSpaceRect);
                setElements(prev => prev.map((el, idx) => {
                    if (idx !== elementIndex) return el;
                    return { ...el, x: tempX, y: tempY };
                }));
            }
            rafId = null;
        };
        const handleMouseMove = (e) => {
            if (element.type === 'img' && !isActuallyDragging.current) {
                const distance = Math.sqrt(Math.pow(e.clientX - dragStartX.current, 2) + Math.pow(e.clientY - dragStartY.current, 2));
                if (distance > 6) {
                    isActuallyDragging.current = true;
                    setDraggingElementId(id);
                } else {
                    return;
                }
            }
            dragStartX.current = e.clientX;
            dragStartY.current = e.clientY;
            if (!rafId) {
                rafId = requestAnimationFrame(() => updateElementPosition());
            }
        };
        const handleMouseUp = () => {
            setAlignmentLines({ vertical: null, horizontal: null });
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
            isResizing = false;
            setDraggingElementId(null);
            isActuallyDragging.current = false;
            setTimeout(() => {
                const dragged = elements[elementIndex];
                if (dragged && selectedIds.includes(dragged.id)) {
                    updateToolbarForElement(dragged);
                }
            }, 0);
            setActiveResize({ elementId: null, corner: null });
        };
        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
        if (isTouch) {
            const handleTouchMove = (te) => {
                console.log('touchmove (resize handle)', te.touches[0].clientX, te.touches[0].clientY);
                lastTouchX = te.touches[0].clientX;
                lastTouchY = te.touches[0].clientY;
                if (!rafId) rafId = requestAnimationFrame(() => updateElementPosition(lastTouchX, lastTouchY));
            };
            const handleTouchEnd = () => {
                setAlignmentLines({ vertical: null, horizontal: null });
                document.removeEventListener("touchmove", handleTouchMove);
                document.removeEventListener("touchend", handleTouchEnd);
                isResizing = false;
                setDraggingElementId(null);
                isActuallyDragging.current = false;
                setTimeout(() => {
                    const dragged = elements[elementIndex];
                    if (dragged && selectedIds.includes(dragged.id)) {
                        updateToolbarForElement(dragged);
                    }
                }, 0);
                setActiveResize({ elementId: null, corner: null });
            };
            document.addEventListener("touchmove", handleTouchMove, { passive: false });
            document.addEventListener("touchend", handleTouchEnd, { passive: false });
        }
    };

    // Handle rotation of elements
    const handleRotationStart = (e, id) => {
        e.stopPropagation();
        if (!selectedIds.includes(id)) {
            return;
        }
        const element = elements.find(el => el.id === id);
        if (!element) return;
        const domElement = document.querySelector(`[data-element-id="${id}"]`);
        const elementRect = domElement.getBoundingClientRect();
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;
        // احسب مركز العنصر مع مراعاة الزوم
        const centerX = (elementRect.left - designSpaceRect.left) / scale + elementRect.width / (2 * scale);
        const centerY = (elementRect.top - designSpaceRect.top) / scale + elementRect.height / (2 * scale);
        // احسب زاوية البداية
        const { x: mouseX, y: mouseY } = getRelativeMousePosition(e, designSpaceRect, zoomLevel);
        const startAngle = Math.atan2(mouseY - centerY, mouseX - centerX) * (180 / Math.PI);
        const initialRotation = element.rotation || 0;
        let lastClientX = 0;
        let lastClientY = 0;
        let rotationRafId = null;
        let isRotating = true;
        const updateElementRotation = () => {
            if (!isRotating || !selectedIds.includes(id)) {
                isRotating = false;
                if (rotationRafId) {
                    cancelAnimationFrame(rotationRafId);
                    rotationRafId = null;
                }
                return;
            }
            // احسب الموضع الحالي للماوس مع مراعاة الزوم
            const { x: moveX, y: moveY } = getRelativeMousePosition({ clientX: lastClientX, clientY: lastClientY }, designSpaceRect, zoomLevel);
            const newAngle = Math.atan2(moveY - centerY, moveX - centerX) * (180 / Math.PI);
            let rotationDelta = newAngle - startAngle;
            const newRotation = initialRotation + rotationDelta;
            if (domElement && selectedIds.includes(id)) {
                domElement.style.transform = `rotate(${newRotation}deg)`;
                domElement.style.transformOrigin = 'center center';
                domElement.style.willChange = 'transform';
                domElement._pendingRotation = newRotation;
            }
            rotationRafId = null;
        };
        const handleRotationMove = (e) => {
            lastClientX = e.clientX;
            lastClientY = e.clientY;
            if (!rotationRafId) {
                rotationRafId = requestAnimationFrame(updateElementRotation);
            }
        };
        const handleRotationEnd = () => {
            isRotating = false;
            if (rotationRafId) {
                cancelAnimationFrame(rotationRafId);
                rotationRafId = null;
            }

            // Remove event listeners
            document.removeEventListener('mousemove', handleRotationMove);
            document.removeEventListener('mouseup', handleRotationEnd);

            // Apply pending rotation to React state only if element is still selected
            if (selectedIds.includes(id)) {
                const domElementFinal = document.querySelector(`[data-element-id="${id}"]`);
                if (domElementFinal && domElementFinal._pendingRotation !== undefined) {
                    updateElement(id, { rotation: domElementFinal._pendingRotation });
                    delete domElementFinal._pendingRotation;
                }
            }
        };

        // Add event listeners
        document.addEventListener('mousemove', handleRotationMove);
        document.addEventListener('mouseup', handleRotationEnd);

        // Add cleanup function to remove event listeners when element is deselected
        const cleanup = () => {
            isRotating = false;
            if (rotationRafId) {
                cancelAnimationFrame(rotationRafId);
                rotationRafId = null;
            }
            document.removeEventListener('mousemove', handleRotationMove);
            document.removeEventListener('mouseup', handleRotationEnd);
        };

        // Store cleanup function on the element
        const domElementForCleanup = document.querySelector(`[data-element-id="${id}"]`);
        if (domElementForCleanup) {
            domElementForCleanup._rotationCleanup = cleanup;
        }
    };

    // Add effect to handle deselection
    useEffect(() => {
        return () => {
            // Cleanup rotation handlers for all elements
            elements.forEach(el => {
                const domElement = document.querySelector(`[data-element-id="${el.id}"]`);
                if (domElement && domElement._rotationCleanup) {
                    domElement._rotationCleanup();
                    delete domElement._rotationCleanup;
                }
            });
        };
    }, [selectedIds]);

    const handleElementClick = (element, e) => {
        e.stopPropagation();
        // احذف شرط منع التحديد للصور
        // Get the design space container
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        
        // Get the element's position and dimensions
        const elementRect = e.currentTarget.getBoundingClientRect();
        const elementTop = elementRect.top - designSpaceRect.top;
        const elementRight = designSpaceRect.right - elementRect.right;
        const elementLeft = elementRect.left - designSpaceRect.left;
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;
        
        // Define the threshold for edge detection (in pixels)
        const edgeThreshold = 50;
        
        // Define the distance between toolbar and element
        const toolbarDistance = 60;
        
        // Determine if element is near edges
        const isNearTop = elementTop < edgeThreshold;
        const isNearRight = elementRight < edgeThreshold;
        const isNearLeft = elementLeft < edgeThreshold;
        
        // Calculate toolbar position
        let position = {
            position: 'absolute',
            display: 'flex',
            zIndex: 1000,
            pointerEvents: 'auto'
        };
    
        // Calculate toolbar position based on element dimensions and position
        if (isNearRight) {
            // Position toolbar on the left side
            position = {
                ...position,
                left: `-${toolbarDistance}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else if (isNearLeft) {
            // Position toolbar on the right side with vertical alignment
            position = {
                ...position,
                left: `${elementWidth + 20}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else {
            // Default position - above the element
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
    
        // If near top edge and not near sides, position below
        if (isNearTop && !isNearRight && !isNearLeft) {
            position = {
                ...position,
                top: 'auto',
                bottom: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
    
        // Update toolbar position state
        setToolbarPosition(position);
        
        // Update toolbar classes
        let classes = '';
        if (isNearTop) classes += ' top-edge';
        if (isNearRight) classes += ' right-edge';
        if (isNearLeft) classes += ' left-edge';
        setToolbarClasses(classes.trim());
        
        // Update selection
        setSelectedElement(element);
        if (e.ctrlKey || e.shiftKey || isMultiSelectActive) {
            setSelectedIds((prevSelectedIds) =>
                prevSelectedIds.includes(element.id)
                    ? prevSelectedIds.filter((selectedId) => selectedId !== element.id)
                    : [...prevSelectedIds, element.id]
            );
        } else {
            setSelectedIds([element.id]);
        }

        // Show mobile hint for first-time users
        if (isMobile && !showMobileHint) {
            setShowMobileHint(true);
            if (hintTimeoutRef.current) {
                clearTimeout(hintTimeoutRef.current);
            }
            hintTimeoutRef.current = setTimeout(() => {
                setShowMobileHint(false);
            }, 4000);
        }

        // --- Prevent sidebar opening on mobile ---
        if (isMobile) return;

        // فتح القائمة المناسبة في الشريط الجانبي بناءً على نوع العنصر
        // استثناء الصور (img) كما طلبت
        if (element.type !== 'img') {
            let targetTab = null;
            
            switch (element.type) {
                case 'text':
                case 'label':
                    targetTab = 'text';
                    break;
                case 'icon':
                    targetTab = 'icons';
                    break;
                case 'qr':
                    targetTab = 'qr';
                    break;
                case 'shape':
                case 'line':
                case 'frame':
                case 'sticker':
                case 'gradient':
                case 'svg':
                    targetTab = 'elements';
                    break;
                default:
                    // إذا لم يكن نوع معروف، لا تفتح أي قائمة
                    break;
            }
            
            // إرسال إشارة لفتح القائمة المناسبة في LeftSidebar
            if (targetTab) {
                // استخدام CustomEvent لإرسال إشارة لفتح القائمة
                const openTabEvent = new CustomEvent('openSidebarTab', {
                    detail: { tabId: targetTab }
                });
                document.dispatchEvent(openTabEvent);
            }
        } else {
            // فتح قائمة Image Editor للصور أو Crop حسب الحالة
            const targetTab = shouldOpenCropTab ? 'crop' : 'image-editor';
            const openTabEvent = new CustomEvent('openSidebarTab', {
                detail: { tabId: targetTab }
            });
            document.dispatchEvent(openTabEvent);
            // إعادة تعيين الحالة
            setShouldOpenCropTab(false);
        }
        // تحديث موضع شريط الأدوات بعد التحديد مباشرة
        setTimeout(() => {
          updateToolbarForElement(element);
        }, 0);
    };

    // Function to get the position of resize handles
    const getResizeHandlePosition = (corner) => {
        switch (corner) {
            case "top-left":
                return { top: "-5px", left: "-5px" };
            case "top-right":
                return { top: "-5px", right: "-5px" };
            case "bottom-left":
                return { bottom: "-5px", left: "-5px" };
            case "bottom-right":
                return { bottom: "-5px", right: "-5px" };
            default:
                return {};
        }
    };

    const showAlignmentLines = (x, y, element, designSpaceRect) => {
        let vertical = null;
        let horizontal = null;

        // Tolerance for alignment
        const tolerance = 5;

        // Check alignment with container
        const containerCenterX = designSpaceRect.width / 2;
        const containerCenterY = designSpaceRect.height / 2;

        if (Math.abs(x + element.width / 2 - containerCenterX) < tolerance) {
            vertical = { position: containerCenterX, type: "solid" };
        }

        if (Math.abs(y + element.height / 2 - containerCenterY) < tolerance) {
            horizontal = { position: containerCenterY, type: "solid" };
        }

        // Check alignment with other elements
        elements.forEach((el) => {
            if (el.id !== element.id) {
                // Horizontal alignment checks
                if (Math.abs(el.y - y) < tolerance) {
                    horizontal = { position: el.y, type: "dotted" }; // Top alignment
                } else if (Math.abs(el.y + el.height - y) < tolerance) {
                    horizontal = { position: el.y + el.height, type: "dotted" }; // Bottom aligns with top
                } else if (Math.abs(el.y + el.height / 2 - (y + element.height / 2)) < tolerance) {
                    horizontal = { position: el.y + el.height / 2, type: "dotted" }; // Center horizontally
                }

                // Vertical alignment checks
                if (Math.abs(el.x - x) < tolerance) {
                    vertical = { position: el.x, type: "dotted" }; // Left alignment
                } else if (Math.abs(el.x + el.width - x) < tolerance) {
                    vertical = { position: el.x + el.width, type: "dotted" }; // Right aligns with left
                } else if (Math.abs(el.x + el.width / 2 - (x + element.width / 2)) < tolerance) {
                    vertical = { position: el.x + el.width / 2, type: "dotted" }; // Center vertically
                }
            }
        });

        setAlignmentLines({ vertical, horizontal });
    };

    // Prevent clicks inside the image sidebar or AI tools from deselecting the element
    const handleMainClick = (e) => {
        // Check if the click is inside the image sidebar or AI tools
        const isImageSidebarClick = e.target.closest('.image-edit-sidebar');
        const isAIToolsClick = e.target.closest('.ai-tools-tabs') || e.target.closest('.ai-tools-hub');
        const isTextAssistantClick = e.target.closest('.text-assistant') || e.target.closest('.p-button') || e.target.closest('.p-dropdown') || e.target.closest('.p-slider');
        const isAIToolsButton = e.target.closest('button') && (
            e.target.closest('button').textContent.includes('Apply Style') ||
            e.target.closest('button').textContent.includes('Enhance Image') ||
            e.target.closest('button').textContent.includes('Change Background') ||
            e.target.closest('button').textContent.includes('Apply to Selected') ||
            e.target.closest('button').textContent.includes('Add to Canvas') ||
            e.target.closest('button').textContent.includes('Reset')
        );

        // Don't deselect if clicking in sidebars, AI tools, text assistant, or specific buttons
        if (!isImageSidebarClick && !isAIToolsClick && !isTextAssistantClick && !isAIToolsButton) {
            setSelectedIds([]);
        }
    };

    useEffect(() => {
        const handleKeyDown = (e) => {
            // تجاهل إذا كان التركيز على input أو textarea أو contentEditable
            const active = document.activeElement;
            if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
                return;
            }

            // نسخ العنصر
            if (e.ctrlKey && e.key.toLowerCase() === 'c') {
                if (selectedIds.length === 1) {
                    const el = elements.find(el => el.id === selectedIds[0]);
                    if (el) {
                        clipboardRef.current = JSON.parse(JSON.stringify(el));
                    }
                }
            }

            // لصق العنصر
            if (e.ctrlKey && e.key.toLowerCase() === 'v') {
                if (clipboardRef.current) {
                    // احسب أعلى zIndex موجود + 1
                    const maxZIndex = elements.length > 0 ? Math.max(...elements.map(el => el.zIndex || 0)) : 0;
                    const newZIndex = maxZIndex + 1;
                    
                    const newElement = {
                        ...JSON.parse(JSON.stringify(clipboardRef.current)),
                        id: `el_${Date.now()}`,
                        x: (clipboardRef.current.x || 50) + 30,
                        y: (clipboardRef.current.y || 50) + 30,
                        zIndex: newZIndex // إضافة zIndex جديد
                    };
                    setElements(prev => [...prev, newElement]);
                    setSelectedIds([newElement.id]);
                }
            }

            // حذف العنصر
            if (e.key === 'Delete' || e.key === 'Backspace') {
                if (selectedIds.length > 0) {
                    setElements(prev => prev.filter(el => !selectedIds.includes(el.id)));
                    setSelectedIds([]);
                }
            }

            // تحريك العنصر بالأسهم
            const moveStep = e.shiftKey ? 20 : 5;
            if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key) && selectedIds.length > 0) {
                e.preventDefault();
                setElements(prev => prev.map(el => {
                    if (!selectedIds.includes(el.id)) return el;
                    let { x, y } = el;
                    if (e.key === 'ArrowUp') y = Math.max(0, y - moveStep);
                    if (e.key === 'ArrowDown') y = y + moveStep;
                    if (e.key === 'ArrowLeft') x = Math.max(0, x - moveStep);
                    if (e.key === 'ArrowRight') x = x + moveStep;
                    return { ...el, x, y };
                }));
            }
        };
        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [elements, selectedIds]);

    // منطق الحفظ الفعلي بدون setSelectedIds([])
    const doSaveDesign = async () => {
        try {
            // تحويل النصوص إلى متغيرات في DOM
            const textElements = designSpaceRef.current.querySelectorAll('.user-data');
            textElements.forEach(element => {
                const value = element.textContent.trim();
                if (value) {
                    element.textContent = `{{${value}}}`;
                }
            });

            const content = designSpaceRef.current.innerHTML;

            // Get the current background from the DOM directly
            const designSpaceContent = document.getElementById('design-space-content');
            let actualBackground = '';
            let actualBackgroundStyle = null;

            if (designSpaceContent) {
                // Get the computed style
                const computedStyle = window.getComputedStyle(designSpaceContent);
                // Get background color and image
                const bgColor = computedStyle.backgroundColor;
                const bgImage = computedStyle.backgroundImage;
                // Handle solid color
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    actualBackground = bgColor;
                }
                // Handle gradient or image
                else if (bgImage && bgImage !== 'none') {
                    actualBackground = bgImage;
                    actualBackgroundStyle = {
                        backgroundSize: computedStyle.backgroundSize,
                        backgroundPosition: computedStyle.backgroundPosition,
                        backgroundRepeat: computedStyle.backgroundRepeat
                    };
                }
            }

            // تحويل النصوص في init_template إلى متغيرات
            const initTemplateElements = elements.map(el => {
                if (el.type === 'text' && el.value) {
                    return {
                        ...el,
                        value: `{{${el.value}}}`
                    };
                }
                return el;
            });

            // Create template data with background information
            const templateData = {
                htmlTemplate: content,
                initTemplate: JSON.stringify(initTemplateElements),
                background: actualBackground || canvasBackgroundStyle?.backgroundColor,
                backgroundStyle: actualBackgroundStyle ? JSON.stringify(actualBackgroundStyle) : ''
            };

            // Update template data
            if (typeof updateTemplateData === 'function') {
                updateTemplateData(templateData);
            }

            // إعادة النصوص إلى حالتها الأصلية
            textElements.forEach(element => {
                const value = element.textContent.trim();
                if (value && value.startsWith('{{') && value.endsWith('}}')) {
                    element.textContent = value.slice(2, -2);
                }
            });
        } catch (error) {
            console.error('Error saving design:', error);
        }
    };

    // زر الحفظ: يلغي التحديد أولاً ثم يطلب الحفظ بعد إعادة التصيير
    const handleSaveClick = () => {
        setSelectedIds([]);
        setPendingSave(true);
    };

    // راقب pendingSave و selectedIds
    useEffect(() => {
        if (pendingSave && selectedIds.length === 0) {
            (async () => {
                try {
                    // تجهيز بيانات التصميم
                    const textElements = designSpaceRef.current.querySelectorAll('.user-data');
                    textElements.forEach(element => {
                        const value = element.textContent.trim();
                        if (value) {
                            element.textContent = `{{${value}}}`;
                        }
                    });
                    const content = designSpaceRef.current.innerHTML;
                    const designSpaceContent = document.getElementById('design-space-content');
                    let actualBackground = '';
                    let actualBackgroundStyle = null;
                    if (designSpaceContent) {
                        const computedStyle = window.getComputedStyle(designSpaceContent);
                        const bgColor = computedStyle.backgroundColor;
                        const bgImage = computedStyle.backgroundImage;
                        if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                            actualBackground = bgColor;
                        } else if (bgImage && bgImage !== 'none') {
                            actualBackground = bgImage;
                            actualBackgroundStyle = {
                                backgroundSize: computedStyle.backgroundSize,
                                backgroundPosition: computedStyle.backgroundPosition,
                                backgroundRepeat: computedStyle.backgroundRepeat
                            };
                        }
                    }
                    // const initTemplateElements = elements.map ... (غير مستخدم)
                    const background = actualBackground || canvasBackgroundStyle?.backgroundColor || design?.background || '';
                    const backgroundStyle = actualBackgroundStyle ? JSON.stringify(actualBackgroundStyle) : (canvasBackgroundStyle ? (typeof canvasBackgroundStyle === 'string' ? canvasBackgroundStyle : JSON.stringify(canvasBackgroundStyle)) : design?.backgroundStyle || '');
                    const templateContent = content;
                    const _formData = new FormData();
                    _formData.append('id', design?.id);
                    _formData.append('name', design?.name || '');
                    _formData.append('template', templateContent);
                    _formData.append('card_type_id', cardType?.id);
                    _formData.append('init_template', JSON.stringify(elements || []));
                    _formData.append('background', background);
                    _formData.append('background_style', backgroundStyle);
                    _formData.append('_method', 'PUT');
                    await updateTemplate.mutateAsync({ data: _formData, id: design?.id });
                    // إعادة النصوص إلى حالتها الأصلية
                    textElements.forEach(element => {
                        const value = element.textContent.trim();
                        if (value && value.startsWith('{{') && value.endsWith('}}')) {
                            element.textContent = value.slice(2, -2);
                        }
                    });
                    // استدعاء فتح مودال معالجة الصور بعد نجاح الحفظ
                    if (typeof onImageGenerationStart === 'function') {
                        onImageGenerationStart();
                    }
                } catch (error) {
                    console.error('Error saving design:', error);
                } finally {
            setPendingSave(false);
                }
            })();
        }
    }, [pendingSave, selectedIds]);

    // Handle crop handle mouse down (سيتم إضافة المنطق لاحقاً)
    const handleCropHandleMouseDown = (e, id, side) => {
        e.stopPropagation();
        setActiveCrop({ elementId: id, side });
        const isTouch = e.touches && e.touches.length > 0;
        // فقط للصور
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        const element = { ...elements[elementIndex] };
        if (element.type !== 'img') return;

        // إعداد القيم الأولية
        const startX = isTouch ? e.touches[0].clientX : e.clientX;
        const startY = isTouch ? e.touches[0].clientY : e.clientY;
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const initialHeight = element.height;

        // حدود التصميم (لمنع الخروج)
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();

        let rafId = null;
        let lastTouchX = startX;
        let lastTouchY = startY;

        let objectPosX = 0.5;
        let objectPosY = 0.5;

        const updateCrop = (touchX, touchY) => {
            console.log('updateCrop', touchX, touchY);
            const clientX = typeof touchX === 'number' ? touchX : lastTouchX;
            const clientY = typeof touchY === 'number' ? touchY : lastTouchY;
            let newX = initialX;
            let newY = initialY;
            let newWidth = initialWidth;
            let newHeight = initialHeight;

            if (side === 'left') {
                const delta = clientX - startX;
                let maxDelta = initialWidth - 40; // لا تقلل أكثر من 40px
                let minDelta = -initialX; // لا تتجاوز حدود التصميم من اليسار
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newX = initialX + appliedDelta;
                newWidth = initialWidth - appliedDelta;
                objectPosX = 1;
            } else if (side === 'right') {
                const delta = startX - clientX;
                let maxDelta = initialWidth - 40;
                let minDelta = -(designSpaceRect.width - (initialX + initialWidth)); // لا تتجاوز حدود التصميم من اليمين
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newWidth = initialWidth - appliedDelta;
                objectPosX = 0;
            } else if (side === 'top') {
                const delta = clientY - startY;
                let maxDelta = initialHeight - 40;
                let minDelta = -initialY;
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newY = initialY + appliedDelta;
                newHeight = initialHeight - appliedDelta;
                objectPosY = 1;
            } else if (side === 'bottom') {
                const delta = startY - clientY;
                let maxDelta = initialHeight - 40;
                let minDelta = -(designSpaceRect.height - (initialY + initialHeight));
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newHeight = initialHeight - appliedDelta;
                objectPosY = 0;
            }

            // عدل العنصر في المصفوفة
            const newElements = [...elements];
            newElements[elementIndex] = {
                ...element,
                x: newX,
                y: newY,
                width: newWidth,
                height: newHeight,
                objectPosX: objectPosX,
                objectPosY: objectPosY
            };
            setElements(newElements);
            rafId = null;
        };

        const handleMouseMove = (ev) => {
            lastTouchX = ev.clientX;
            lastTouchY = ev.clientY;
            if (!rafId) rafId = requestAnimationFrame(() => updateCrop());
        };
        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            setActiveCrop({ elementId: null, side: null });
        };
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        if (isTouch) {
            const handleTouchMove = (te) => {
                console.log('touchmove (crop handle)', te.touches[0].clientX, te.touches[0].clientY);
                lastTouchX = te.touches[0].clientX;
                lastTouchY = te.touches[0].clientY;
                if (!rafId) rafId = requestAnimationFrame(() => updateCrop(lastTouchX, lastTouchY));
            };
            const handleTouchEnd = () => {
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);
                setActiveCrop({ elementId: null, side: null });
            };
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd, { passive: false });
        }
    };

    // Handle text frame resize handles
    const handleTextFrameResize = (e, id, side) => {
        e.stopPropagation();
        setActiveTextFrameResize({ elementId: id, side });
        const isTouch = e.touches && e.touches.length > 0;
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        const element = { ...elements[elementIndex] };
        if (element.type !== 'text' && element.type !== 'label') return;
        const startX = isTouch ? e.touches[0].clientX : e.clientX;
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;
        let rafId = null;
        let lastTouchX = startX;
        const updateTextFrame = (touchX) => {
            console.log('updateTextFrame', touchX);
            const clientX = typeof touchX === 'number' ? touchX : lastTouchX;
            let newX = initialX;
            let newY = initialY;
            const MARGIN = 8;

            let newWidth = initialWidth;
            // 1. احسب العرض الجديد
            if (side === 'left') {
                const mouseX = clientX - designSpaceRect.left;
                // السماح بالتصغير حتى minWidth حتى لو كان ملاصق للهامش
                let dynamicMinWidth = 40;
                const isDynamic = element.isDynamicField || (typeof element.value === 'string' && element.value.includes('{{'));
                if (isDynamic && typeof element.value === 'string') {
                    // احسب عرض أطول سطر
                    const lines = element.value.split('\n');
                    let maxLineWidth = 0;
                    lines.forEach(line => {
                        const { width: lineWidth } = measureText(
                            line,
                            element.fontSize || 16,
                            element.fontFamily || 'Arial, sans-serif',
                            element.fontWeight || 'normal',
                            element.fontStyle || 'normal',
                            element.lineHeight || 1.2
                        );
                        if (lineWidth > maxLineWidth) maxLineWidth = lineWidth;
                    });
                    // أضف نفس الهامش المستخدم في الإضافة (مثلاً 12 بكسل)
                    dynamicMinWidth = Math.max(maxLineWidth + 12, 40);
                }
                let minDelta = -(initialWidth - dynamicMinWidth);
                // maxDelta يسمح بالتكبير حتى حدود التصميم (من اليسار)
                let maxDelta = initialX - MARGIN;
                let delta = initialX - mouseX;
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newX = initialX - appliedDelta;
                newWidth = initialWidth + appliedDelta;
                // منع الخروج عن حدود التصميم من اليسار
                if (newX < MARGIN) {
                    // لا تعدل newX، فقط نقص العرض
                    newWidth -= (MARGIN - newX);
                    newX = MARGIN;
                }
                // منع الخروج عن حدود التصميم من اليمين (قبل النهاية بـ 20px)
                const canvasWidth = designSpaceRect.width / scale;
                const RIGHT_MARGIN = 20;
                if (newX + newWidth > canvasWidth - RIGHT_MARGIN) {
                    newWidth = canvasWidth - RIGHT_MARGIN - newX;
                }
            } else if (side === 'right') {
                const mouseX = clientX - designSpaceRect.left;
                let maxDelta = designSpaceRect.width / scale - (initialX + initialWidth); // لا تتجاوز حدود التصميم
                let minDelta = -(initialWidth - 40); // لا تقلل أكثر من 40px
                let delta = mouseX - (initialX + initialWidth);
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newWidth = initialWidth + appliedDelta;
            }
            // 2. احسب الحد الأدنى للعرض إذا كان dynamic field
            let minWidth = 40;
            const paddingW = 12;
            if (element.isDynamicField || (typeof element.value === 'string' && element.value.includes('{{'))) {
                // احسب عرض أطول كلمة (أو متغير)
                let text = element.value || '';
                // أزل الأقواس إذا كان متغير
                if (text.startsWith('{{') && text.endsWith('}}')) {
                    text = text.slice(2, -2);
                }
                // split by space or underscore or dash
                const words = text.split(/\s+|_|-/);
                let maxWordWidth = 0;
                for (let word of words) {
                    const { width: w } = measureText(
                        word,
                        element.fontSize || 16,
                        element.fontFamily || 'Arial, sans-serif',
                        element.fontWeight || 'normal',
                        element.fontStyle || 'normal',
                        element.lineHeight || 1.2
                    );
                    if (w > maxWordWidth) maxWordWidth = w;
                }
                minWidth = Math.max(maxWordWidth + paddingW, 40);
            }
            // لا تصغر أقل من minWidth
            newWidth = Math.max(newWidth, minWidth);
            // 3. احسب ارتفاع النص بعد الالتفاف
            const paddingH = 8;
            const minHeight = 24;
            const { height: measuredHeight } = measureText(
                element.value,
                element.fontSize || 16,
                element.fontFamily || 'Arial, sans-serif',
                element.fontWeight || 'normal',
                element.fontStyle || 'normal',
                element.lineHeight || 1.2,
                newWidth // هنا نمرر العرض الجديد ليتم الالتفاف
            );
            let newHeight = Math.max(measuredHeight + paddingH, minHeight);

            // 4. حدّث العنصر
            setElements(prev => prev.map((el, idx) => {
                if (idx !== elementIndex) return el;
                return { ...el, x: newX, y: newY, width: newWidth, height: newHeight };
            }));

            // تحديث مباشر في DOM
            const domElement = document.querySelector(`[data-element-id="${id}"]`);
            if (domElement) {
                domElement.style.left = `${newX}px`;
                domElement.style.top = `${newY}px`;
                domElement.style.width = `${newWidth}px`;
                domElement.style.height = `${newHeight}px`;
                domElement.style.transition = 'none';
            }

            // إضافة تأثير بصري للعنصر أثناء التغيير
            if (domElement) {
                domElement.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.3)';
            }

            // تحديث موضع شريط الأدوات أثناء السحب
            updateToolbarForElement(element, { x: newX, y: newY, width: newWidth, height: newHeight });

            rafId = null;
        };

        const handleMouseMove = (ev) => {
            lastTouchX = ev.clientX;
            if (!rafId) rafId = requestAnimationFrame(() => updateTextFrame());
        };

        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            const domElement = document.querySelector(`[data-element-id="${id}"]`);
            if (domElement) {
                domElement.style.boxShadow = '';
                domElement.style.transition = 'all 0.2s ease';
            }
            setActiveTextFrameResize({ elementId: null, side: null });
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        if (isTouch) {
            const handleTouchMove = (te) => {
                console.log('touchmove (text frame)', te.touches[0].clientX);
                lastTouchX = te.touches[0].clientX;
                if (!rafId) rafId = requestAnimationFrame(() => updateTextFrame(lastTouchX));
            };
            const handleTouchEnd = () => {
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);
                const domElement = document.querySelector(`[data-element-id="${id}"]`);
                if (domElement) {
                    domElement.style.boxShadow = '';
                    domElement.style.transition = 'all 0.2s ease';
                }
                setActiveTextFrameResize({ elementId: null, side: null });
            };
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd, { passive: false });
        }
    };

    // عند تحميل التصميم أو تغييره
    useEffect(() => {
        if (design?.elements) {
            const loadedElements = JSON.parse(JSON.stringify(design.elements));
            // تعيين zIndex للعناصر المحملة إذا لم تكن موجودة
            const elementsWithZIndex = loadedElements.map((el, index) => ({
                ...el,
                zIndex: el.zIndex || index + 1
            }));
            setInitialElements(elementsWithZIndex);
            
            // تحديث العناصر في السياق مع zIndex
            setElements(elementsWithZIndex);
        } else if (elements.length > 0) {
            setInitialElements(JSON.parse(JSON.stringify(elements)));
        }
        if (canvasBackgroundStyle) {
            setInitialBackgroundStyle(JSON.parse(JSON.stringify(canvasBackgroundStyle)));
        }
    }, [design, cardType]);

    // مقارنة التعديلات
    useEffect(() => {
        const elementsEqual = JSON.stringify(elements) === JSON.stringify(initialElements);
        const bgEqual = JSON.stringify(canvasBackgroundStyle) === JSON.stringify(initialBackgroundStyle);
        setIsDirty(!(elementsEqual && bgEqual));
    }, [elements, canvasBackgroundStyle, initialElements, initialBackgroundStyle]);

    // بعد الحفظ، أعد تعيين الأصل
    useEffect(() => {
        if (!isDirty && elements.length > 0) {
            setInitialElements(JSON.parse(JSON.stringify(elements)));
            setInitialBackgroundStyle(JSON.parse(JSON.stringify(canvasBackgroundStyle)));
        }
    }, [isDirty]);

    // Auto-fit text box when fontSize or value changes
    useEffect(() => {
        elements.forEach((el, idx) => {
            if ((el.type === 'text' || el.type === 'label') && (el.value || el.fontSize)) {
                // Only auto-fit if not currently resizing horizontally or using text frame handles
                if (!activeResize.elementId || activeResize.elementId !== el.id || (activeResize.corner !== 'left' && activeResize.corner !== 'right')) {
                    // Also check if not using text frame resize handles
                    if (!activeTextFrameResize.elementId || activeTextFrameResize.elementId !== el.id) {
                        const { width, height } = measureText(
                            el.value,
                            el.fontSize || 16,
                            el.fontFamily || 'Arial, sans-serif',
                            el.fontWeight || 'normal',
                            el.fontStyle || 'normal',
                            el.lineHeight || 1.2
                        );
                        // Add some min width/height
                        const minWidth = 40;
                        const minHeight = 24;
                        // Only auto-fit if the element hasn't been manually resized
                        // Check if current width is significantly different from calculated width
                        const widthDifference = Math.abs((el.width || 0) - width);
                        const heightDifference = Math.abs((el.height || 0) - height);
                        if (widthDifference < 5 && heightDifference < 5) {
                            if (((el.width || 0) < width - 2 || (el.height || 0) < height - 2)) {
                                // ضبط الأبعاد داخل حدود التصميم
                                let dims = fitTextElementWithinBounds(el.x, el.y, Math.max(width, minWidth), Math.max(height, minHeight), cardType);
                                setElements(prev => prev.map((item, i) => i === idx ? { ...item, ...dims } : item));
                            }
                        }
                    }
                }
            }
        });
    }, [elements.map(el => el.type === 'text' || el.type === 'label' ? `${el.value}|${el.fontSize}|${el.fontFamily}|${el.fontWeight}|${el.fontStyle}|${el.lineHeight}` : '').join(','), activeResize, activeTextFrameResize]);

    // 1. أضف دالة للحفظ مع تغيير الاسم (فتح modal فقط)
    const handleSaveAsClick = () => {
        if (typeof dialogHandler === 'function') {
            dialogHandler("createDesignTemplate");
        }
    };

    useEffect(() => {
        window.updateTemplateDataFromDesignSpace = (cb) => {
            try {
                const textElements = designSpaceRef.current.querySelectorAll('.user-data');
                textElements.forEach(element => {
                    const value = element.textContent.trim();
                    if (value) {
                        element.textContent = `{{${value}}}`;
                    }
                });
                const content = designSpaceRef.current.innerHTML;
                // استخراج الخلفية
                const designSpaceContent = document.getElementById('design-space-content');
                let actualBackground = '';
                let actualBackgroundStyle = null;
                if (designSpaceContent) {
                    const computedStyle = window.getComputedStyle(designSpaceContent);
                    const bgColor = computedStyle.backgroundColor;
                    const bgImage = computedStyle.backgroundImage;
                    if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                        actualBackground = bgColor;
                    } else if (bgImage && bgImage !== 'none') {
                        actualBackground = bgImage;
                        actualBackgroundStyle = {
                            backgroundSize: computedStyle.backgroundSize,
                            backgroundPosition: computedStyle.backgroundPosition,
                            backgroundRepeat: computedStyle.backgroundRepeat
                        };
                    }
                }
                const templateData = {
                    htmlTemplate: content,
                    background: actualBackground,
                    backgroundStyle: actualBackgroundStyle ? JSON.stringify(actualBackgroundStyle) : ''
                };
                if (typeof updateTemplateData === 'function') {
                    updateTemplateData(templateData);
                }
                // إعادة النصوص لحالتها الأصلية
                textElements.forEach(element => {
                    const value = element.textContent.trim();
                    if (value && value.startsWith('{{') && value.endsWith('}}')) {
                        element.textContent = value.slice(2, -2);
                    }
                });
                if (cb) cb();
            } catch (e) {
                if (cb) cb();
            }
        };
    }, [updateTemplateData, designSpaceRef]);

    // --- Touch drag state for mobile ---
    const touchDragRef = useRef({
        id: null,
        startX: 0,
        startY: 0,
        initialX: 0,
        initialY: 0,
        moved: false
    });

    // --- Touch resize state for mobile ---
    const touchResizeRef = useRef({
        id: null,
        corner: null,
        startX: 0,
        startY: 0,
        initialWidth: 0,
        initialHeight: 0,
        initialX: 0,
        initialY: 0,
        active: false
    });

    // --- Pinch zoom state for mobile ---
    const pinchRef = useRef({
        id: null,
        initialDistance: 0,
        initialWidth: 0,
        initialHeight: 0,
        initialFontSize: 0,
        active: false,
        center: { x: 0, y: 0 }
    });

    // --- Touch drag handlers for mobile ---
    const handleTouchStartElement = (e, id) => {
        if (!isMobile) return;
        if (e.touches.length !== 1) return;
        // e.preventDefault(); // تم الحذف لحل مشكلة passive event listener
        e.stopPropagation();
        const touch = e.touches[0];
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        const element = elements[elementIndex];
        touchDragRef.current = {
            id,
            startX: touch.clientX,
            startY: touch.clientY,
            initialX: element.x,
            initialY: element.y,
            moved: false
        };
        setDraggingElementId(id);
        // Select the element if not already selected
        if (!selectedIds.includes(id)) {
            setSelectedElement(element);
            setSelectedIds([id]);
        }
    };
    const handleTouchEndElement = () => {
        if (!isMobile) return;
        setDraggingElementId(null);
        touchDragRef.current = { id: null, startX: 0, startY: 0, initialX: 0, initialY: 0, moved: false };
    };

  

    // --- Pinch-to-zoom handler for mobile ---
    useEffect(() => {
      if (!isMobile) return;
      const designSpace = designSpaceRef.current;
      if (!designSpace) return;

      function getDistance(touches) {
        const dx = touches[0].clientX - touches[1].clientX;
        const dy = touches[0].clientY - touches[1].clientY;
        return Math.sqrt(dx * dx + dy * dy);
      }

      const isToolbarOrButton = (e) => {
        return (
          e.target.closest('.element-controls') ||
          e.target.closest('button') ||
          e.target.closest('.color-picker-container')
        );
      };

      let pinchElementId = null;
      let initialDistance = null;
      let initialWidth = null;
      let initialHeight = null;
      let initialFontSize = null;
      let initialX = null;
      let initialY = null;
      let elementType = null;

      const handleTouchStart = (e) => {
        if (isToolbarOrButton(e)) return;
        if (e.touches.length === 2 && selectedIds.length === 1) {
          pinchElementId = selectedIds[0];
          const el = elements.find(el => el.id === pinchElementId);
          if (!el) return;
          initialDistance = getDistance(e.touches);
          initialWidth = el.width;
          initialHeight = el.height;
          initialFontSize = el.fontSize;
          initialX = el.x;
          initialY = el.y;
          elementType = el.type;
        }
      };

      const handleTouchMove = (e) => {
        if (isToolbarOrButton(e)) return;
        if (e.touches.length === 2 && pinchElementId) {
          const elIdx = elements.findIndex(el => el.id === pinchElementId);
          if (elIdx === -1) return;
          const el = elements[elIdx];
          const newDistance = getDistance(e.touches);
          const scale = newDistance / (initialDistance || 1);
          let newWidth = initialWidth * scale;
          let newHeight = initialHeight * scale;
          let newFontSize = initialFontSize ? Math.max(8, Math.round(initialFontSize * scale)) : undefined;
          // احفظ مركز العنصر
          const centerX = initialX + initialWidth / 2;
          const centerY = initialY + initialHeight / 2;
          // احسب x/y الجديدين للحفاظ على المركز
          let newX = centerX - newWidth / 2;
          let newY = centerY - newHeight / 2;
          // لا تخرج خارج حدود التصميم
          const designSpaceRect = designSpace.getBoundingClientRect();
          const scaleCanvas = zoomLevel / 100;
          const maxW = designSpaceRect.width / scaleCanvas;
          const maxH = designSpaceRect.height / scaleCanvas;
          newX = Math.max(0, Math.min(newX, maxW - newWidth));
          newY = Math.max(0, Math.min(newY, maxH - newHeight));
          newWidth = Math.max(20, Math.min(newWidth, maxW));
          newHeight = Math.max(20, Math.min(newHeight, maxH));
          // تحديث العنصر
          setElements(prev => prev.map((item, idx) => {
            if (idx !== elIdx) return item;
            if (elementType === 'text' || elementType === 'label') {
              return { ...item, x: newX, y: newY, width: newWidth, height: newHeight, fontSize: newFontSize };
            } else {
              return { ...item, x: newX, y: newY, width: newWidth, height: newHeight };
            }
          }));
          // تحديث مباشر في DOM (اختياري)
          const domElement = document.querySelector(`[data-element-id="${pinchElementId}"]`);
          if (domElement) {
            domElement.style.left = `${newX}px`;
            domElement.style.top = `${newY}px`;
            domElement.style.width = `${newWidth}px`;
            domElement.style.height = `${newHeight}px`;
            if (elementType === 'text' || elementType === 'label') {
              domElement.style.fontSize = `${newFontSize}px`;
            }
            domElement.style.transition = 'none';
          }
          e.preventDefault();
        }
      };

      const handleTouchEnd = (e) => {
        pinchElementId = null;
        initialDistance = null;
        initialWidth = null;
        initialHeight = null;
        initialFontSize = null;
        initialX = null;
        initialY = null;
        elementType = null;
      };

      designSpace.addEventListener('touchstart', handleTouchStart, { passive: false });
      designSpace.addEventListener('touchmove', handleTouchMove, { passive: false });
      designSpace.addEventListener('touchend', handleTouchEnd, { passive: false });
      return () => {
        designSpace.removeEventListener('touchstart', handleTouchStart);
        designSpace.removeEventListener('touchmove', handleTouchMove);
        designSpace.removeEventListener('touchend', handleTouchEnd);
      };
    }, [isMobile, designSpaceRef, selectedIds, elements, zoomLevel]);

    // --- تحسين أداء تحريك العناصر في الموبايل ---
    const touchMoveRaf = useRef(null);
    const touchMovePending = useRef(false);
    const lastTouchMove = useRef(null);
    const lastTouchMoveElementIndex = useRef(null);
    const lastTouchMoveElement = useRef(null);

    const handleTouchMoveElement = (e) => {
      if (!isMobile) return;
      if (e.touches.length !== 1) return;
      const drag = touchDragRef.current;
      if (!drag.id) return;
      const touch = e.touches[0];
      const dx = touch.clientX - drag.startX;
      const dy = touch.clientY - drag.startY;
      if (Math.abs(dx) > 2 || Math.abs(dy) > 2) drag.moved = true;
      const elementIndex = elements.findIndex(el => el.id === drag.id);
      if (elementIndex === -1) return;
      const designSpace = designSpaceRef.current;
      const designSpaceRect = designSpace.getBoundingClientRect();
      const scale = zoomLevel / 100;
      let newX = drag.initialX + dx / scale;
      let newY = drag.initialY + dy / scale;
      // Prevent out of bounds
      const el = elements[elementIndex];
      newX = Math.max(0, Math.min(newX, designSpaceRect.width / scale - el.width));
      newY = Math.max(0, Math.min(newY, designSpaceRect.height / scale - el.height));
      // تحسين الأداء: حدث موضع العنصر مباشرة في DOM أثناء السحب
      const domElement = document.querySelector(`[data-element-id="${drag.id}"]`);
      if (domElement) {
        domElement.style.left = `${newX}px`;
        domElement.style.top = `${newY}px`;
        domElement.style.transition = 'none';
      }
      // استخدم requestAnimationFrame لتقليل setState
      lastTouchMove.current = { newX, newY };
      lastTouchMoveElementIndex.current = elementIndex;
      lastTouchMoveElement.current = el;
      if (!touchMovePending.current) {
        touchMovePending.current = true;
        touchMoveRaf.current = requestAnimationFrame(() => {
          setElements(prev => prev.map((item, idx) => idx === lastTouchMoveElementIndex.current ? { ...item, x: lastTouchMove.current.newX, y: lastTouchMove.current.newY } : item));
          touchMovePending.current = false;
        });
      }
    };

    // --- توحيد منطق resize handles في الموبايل ---
    // أضف onTouchStart لنفس دوال handleMouseDown وhandleTextFrameResize وhandleCropHandleMouseDown
    // (تمت إضافتها بالفعل في الكود الحالي، فقط تأكد من أن جميع الدوال تدعم e.touches)
    // لذلك، في handleMouseDown وhandleTextFrameResize وhandleCropHandleMouseDown، أضف دعمًا للـ touch events:
    // مثال: const startX = e.touches ? e.touches[0].clientX : e.clientX;
    // كرر ذلك في جميع الدوال التي تستخدم clientX/clientY

    return (
        <div className={`flex ${isMobile ? 'flex-col' : 'flex-col'} relative w-full items-start h-full`} onClick={handleMainClick}> {/*should probably refractor it*/}
            {/* Top Toolbar - Dark Background - Hide on mobile */}
            {!isMobile && (
                <div className="w-full bg-gray-800 text-white p-2">
                    {/* Placeholder for top toolbar */}
                </div>
            )}

            {/* Canva Toolbar with Save Button - Hide on mobile */}
            {!isMobile && (
                <CanvaToolbar
                    updateTemplateData={updateTemplateData}
                    saveDesignHandler={handleSaveClick}
                    saveAsHandler={onSaveAsDesign}
                    isDirty={isDirty}
                    isCreateMode={!design}
                />
            )}

            {/* Second Toolbar */}
            <div className={`w-full flex items-center p-2 bg-gray-50 border-b border-gray-200 ${isMobile ? 'flex-nowrap gap-2' : ''}`}>
                {/* Left: Back Button */}
                <div className="flex items-center flex-shrink-0">
                    <motion.button
                        className="flex items-center px-3 py-1.5 rounded-md bg-gradient-to-r from-gray-800 to-gray-700 text-white shadow-sm"
                        style={{ minWidth: 70 }}
                        onClick={() => window.history.back()}
                        whileHover={{
                            scale: 1.05,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.2)"
                        }}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <motion.span
                            animate={{ x: [0, -3, 0] }}
                            transition={{ duration: 1.5, repeat: Infinity, repeatType: "loop" }}
                        >
                            <FiChevronLeft size={18} />
                        </motion.span>
                        <span className="ml-1 font-medium">Back</span>
                    </motion.button>
                </div>

                {/* Center: Card Type Dropdown and Save Button (mobile only) */}
                {isMobile && (
                    <div className="flex-1 flex justify-center items-center gap-2">
                        <TypeControl hideLabel={true} />
                        <motion.button
                            className="px-3 py-1.5 rounded-md bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg text-sm font-medium"
                            onClick={handleSaveClick}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            title="Save Design"
                        >
                            Save
                        </motion.button>
                    </div>
                )}

                {/* Right: Other Controls (includes TypeControl on web) */}
                <div className="flex items-center flex-shrink-0 ml-auto">
                    {!isMobile && <TypeControl />}
                    {!isMobile && <AlignmentControl />}
                    {!isMobile && <ResizeInputs />}
                    {!isMobile && <DuplicateControl />}
                    {!isMobile && <DeleteControl />}
                    <button
                        className="ml-2 p-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg hover:from-blue-600 hover:to-cyan-600 flex items-center justify-center border-2 border-white/70"
                        style={{ boxShadow: '0 4px 16px 0 rgba(0, 180, 255, 0.15)' }}
                        onClick={() => document.querySelector('.help-guide-button')?.click()}
                        title="Help"
                    >
                        <FiHelpCircle size={isMobile ? 18 : 22} className="drop-shadow" />
                    </button>
                </div>
            </div>

            {/* Main Content Area - Responsive layout */}
            <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} w-full h-full overflow-hidden`}>
                {/* Left Sidebar - Desktop only */}
                {!isMobile && <LeftSidebar isMobile={isMobile} />}

                {/* Canvas Area */}
                <div className={`flex-grow flex flex-col h-full ${isMobile ? 'pb-24' : ''}`}>
                    {cardType && (
                        <div className={`flex-grow flex justify-center items-center ${isMobile ? 'p-2' : 'p-4'} overflow-auto relative design-space-container`}>
                            <DesignSpaceBackground />
                            <div
                                className={`design-space relative shadow-xl ${isMobile ? 'mobile-canvas' : ''} ${zoomLevel !== 100 ? 'no-interactive-zoom' : ''}`}
                                ref={designSpaceRef}
                                style={{
                                    transform: `scale(${zoomLevel / 100})`,
                                    transformOrigin: 'center center',
                                    willChange: 'transform',
                                    transition: 'transform 0.12s cubic-bezier(0.4,0,0.2,1)',
                                    ...(isMobile && {
                                        maxWidth: 'calc(100vw - 16px)',
                                        maxHeight: 'calc(100vh - 200px)'
                                    })
                                }}
                            >
                                <div
                                    id="design-space-content"
                                    onContextMenu={(e) => e.preventDefault()}
                                    style={{
                                        width: `${cardType?.width}px`,
                                        height: `${cardType?.height}px`,
                                        position: "relative",
                                        backgroundColor: "transparent",
                                        backgroundImage: 'none',
                                        boxShadow: '0 0 40px rgba(0, 0, 0, 0.25)',
                                        backgroundSize: canvasBackgroundStyle?.backgroundSize,
                                        backgroundBlendMode: 'normal',
                                        backgroundPosition: canvasBackgroundStyle?.backgroundPosition || 'center',
                                        backgroundRepeat: canvasBackgroundStyle?.backgroundRepeat || 'repeat',
                                        opacity: 1
                                    }}
                                >
                                    {/* Professional corner marks */}
                                    <div className="corner-mark top-left"></div>
                                    <div className="corner-mark top-right"></div>
                                    <div className="corner-mark bottom-left"></div>
                                    <div className="corner-mark bottom-right"></div>
                                    {elements?.map((el) => (
                                        <div
                                            key={el.id}
                                            data-element-id={el.id}
                                            style={{
                                                position: "absolute",
                                                top: el.y,
                                                left: el.x,
                                                width: el.width,
                                                height: el.height,
                                                cursor: "move",
                                                zIndex: el.zIndex || 0,
                                                transform: el.rotation ? `rotate(${el.rotation}deg)` : undefined,
                                                transformOrigin: 'center center'
                                            }}
                                            className={`draggable-element ${selectedIds.includes(el.id) ? 'selected' : ''}`}
                                            onMouseDown={(e) => handleMouseDown(e, el.id)}
                                            onClick={(e) => handleElementClick(el, e)}
                                            {...(isMobile ? {
                                                onTouchStart: (e) => handleTouchStartElement(e, el.id),
                                                onTouchMove: handleTouchMoveElement,
                                                onTouchEnd: handleTouchEndElement
                                            } : {})}
                                        >
                                            {/* Apply image-specific styles only to the content, not the toolbar */}
                                            <div 
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                    ...(el.style || {})
                                                }}
                                            >
                                                <Element el={el} userData={design?.userData || {}} selectedIds={selectedIds} />
                                            </div>
                                            {/* Crop handles for images only, at mid-sides */}
                                            {selectedIds.includes(el.id) && el.type === 'img' && (
                                                <>
                                                    {/* Top (منتصف الأعلى) */}
                                                    <div
                                                        className="crop-handle crop-handle-top"
                                                        style={{
                                                            position: 'absolute',
                                                            top: '-7px',
                                                            left: '50%',
                                                            transform: 'translateX(-50%)',
                                                            cursor: 'ns-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'top')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleCropHandleMouseDown(e, el.id, 'top');
                                                            }
                                                          } : {})}
                                                    />
                                                    {/* Bottom (منتصف الأسفل) */}
                                                    <div
                                                        className="crop-handle crop-handle-bottom"
                                                        style={{
                                                            position: 'absolute',
                                                            bottom: '-7px',
                                                            left: '50%',
                                                            transform: 'translateX(-50%)',
                                                            cursor: 'ns-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'bottom')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleCropHandleMouseDown(e, el.id, 'bottom');
                                                            }
                                                          } : {})}
                                                    />
                                                    {/* Left (منتصف اليسار) */}
                                                    <div
                                                        className="crop-handle crop-handle-left"
                                                        style={{
                                                            position: 'absolute',
                                                            left: '-7px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            cursor: 'ew-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'left')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleCropHandleMouseDown(e, el.id, 'left');
                                                            }
                                                          } : {})}
                                                    />
                                                    {/* Right (منتصف اليمين) */}
                                                    <div
                                                        className="crop-handle crop-handle-right"
                                                        style={{
                                                            position: 'absolute',
                                                            right: '-7px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            cursor: 'ew-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'right')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleCropHandleMouseDown(e, el.id, 'right');
                                                            }
                                                          } : {})}
                                                    />
                                                </>
                                            )}

                                            {/* Text Frame Resize Handles - للعناصر النصية فقط */}
                                            {selectedIds.includes(el.id) && (el.type === 'text' || el.type === 'label') && (
                                                <>
                                                    {/* Left handle */}
                                                    <div
                                                        className={`text-frame-handle text-frame-handle-left ${activeTextFrameResize.elementId === el.id && activeTextFrameResize.side === 'left' ? 'active' : ''}`}
                                                        style={{
                                                            position: 'absolute',
                                                            left: '-7px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            zIndex: 99999
                                                        }}
                                                        onMouseDown={(e) => handleTextFrameResize(e, el.id, 'left')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleTextFrameResize(e, el.id, 'left');
                                                            }
                                                          } : {})}
                                                        title="Drag to resize text frame width"
                                                    >
                                                        <div className="w-[14px] h-[14px] rounded-full bg-white border border-gray-300"></div>
                                                    </div>
                                                    {/* Right handle */}
                                                    <div
                                                        className={`text-frame-handle text-frame-handle-right ${activeTextFrameResize.elementId === el.id && activeTextFrameResize.side === 'right' ? 'active' : ''}`}
                                                        style={{
                                                            position: 'absolute',
                                                            right: '-7px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            zIndex: 99999
                                                        }}
                                                        onMouseDown={(e) => handleTextFrameResize(e, el.id, 'right')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleTextFrameResize(e, el.id, 'right');
                                                            }
                                                          } : {})}
                                                        title="Drag to resize text frame width"
                                                    >
                                                        <div className="w-[14px] h-[14px] rounded-full bg-white border border-gray-300"></div>
                                                    </div>
                                                </>
                                            )}

                                            {selectedIds.includes(el.id) && draggingElementId !== el.id && !isActuallyDragging.current &&
                                              // إخفاء شريط الأدوات أثناء تغيير حجم إطار النص
                                              (!activeResize.elementId && !(activeTextFrameResize.elementId === el.id)) && (
                                                <>
                                                  {/* Element Controls */}
                                                  <div className={`element-controls element-controls-sm ${toolbarClasses}`} style={{...toolbarPosition, zIndex: 99999}}>
                                                    {/* زر أيقونة لتغيير اللون بنفس شكل ColorPicker */}
                                                    <button
                                                      className="element-control-btn color-picker-btn"
                                                      title="change color"
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        // إرسال حدث إغلاق القوائم
                                                        const closeEvent = new CustomEvent('closeSidebarTabs');
                                                        document.dispatchEvent(closeEvent);
                                                        setTimeout(() => {
                                                          setColorPickerTargetId(el.id);
                                                        }, 100);
                                                      }}
                                                      {...(isMobile ? {
                                                        onTouchStart: (e) => e.stopPropagation(),
                                                        onTouchEnd: (e) => {
                                                          e.preventDefault();
                                                          e.stopPropagation();
                                                          const closeEvent = new CustomEvent('closeSidebarTabs');
                                                          document.dispatchEvent(closeEvent);
                                                          setTimeout(() => {
                                                            setColorPickerTargetId(el.id);
                                                          }, 100);
                                                        }
                                                      } : {})}
                                                    >
                                                      <MdOutlineColorLens size={18} />
                                                    </button>
                                                    {/* زر Crop للصور فقط */}
                                                    {el.type === 'img' && (
                                                      <button
                                                        className="element-control-btn crop-btn"
                                                        title="Crop Image"
                                                        onClick={(e) => {
                                                          e.preventDefault();
                                                          e.stopPropagation();
                                                          setShouldOpenCropTab(true);
                                                          // إرسال إشارة لفتح قائمة Crop
                                                          const openTabEvent = new CustomEvent('openSidebarTab', {
                                                            detail: { tabId: 'crop' }
                                                          });
                                                          document.dispatchEvent(openTabEvent);
                                                        }}
                                                        {...(isMobile ? {
                                                          onTouchStart: (e) => e.stopPropagation(),
                                                          onTouchEnd: (e) => {
                                                            e.preventDefault();
                                                            e.stopPropagation();
                                                            setShouldOpenCropTab(true);
                                                            const openTabEvent = new CustomEvent('openSidebarTab', {
                                                              detail: { tabId: 'crop' }
                                                            });
                                                            document.dispatchEvent(openTabEvent);
                                                          }
                                                        } : {})}
                                                      >
                                                        <FiCrop size={18} />
                                                      </button>
                                                    )}
                                                    <button
                                                      className="element-control-btn rotate-btn"
                                                      title="Rotate 90°"
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        const currentRotation = el.rotation || 0;
                                                        updateElement(el.id, { rotation: currentRotation + 90 });
                                                      }}
                                                      {...(isMobile ? {
                                                        onTouchStart: (e) => e.stopPropagation(),
                                                        onTouchEnd: (e) => {
                                                          e.preventDefault();
                                                          e.stopPropagation();
                                                          const currentRotation = el.rotation || 0;
                                                          updateElement(el.id, { rotation: currentRotation + 90 });
                                                        }
                                                      } : {})}
                                                    >
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                                                        <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                                                      </svg>
                                                    </button>
                                                    <button
                                                      className="element-control-btn forward-btn"
                                                      title="Bring Forward"
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        bringToFront(el.id);
                                                      }}
                                                      {...(isMobile ? {
                                                        onTouchStart: (e) => e.stopPropagation(),
                                                        onTouchEnd: (e) => {
                                                          e.preventDefault();
                                                          e.stopPropagation();
                                                          bringToFront(el.id);
                                                        }
                                                      } : {})}
                                                    >
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M8 6.5a.5.5 0 0 1 .5.5v1.5H10a.5.5 0 0 1 0 1H8.5V11a.5.5 0 0 1-1 0V9.5H6a.5.5 0 0 1 0-1h1.5V7a.5.5 0 0 1 .5-.5z"/>
                                                        <path d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-7 7 7 7 0 0 0 7 7 7 7 0 0 0 7-7 7 7 0 0 0-7-7z"/>
                                                      </svg>
                                                    </button>
                                                    <button
                                                      className="element-control-btn backward-btn"
                                                      title="Send Backward"
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        sendToBack(el.id);
                                                      }}
                                                      {...(isMobile ? {
                                                        onTouchStart: (e) => e.stopPropagation(),
                                                        onTouchEnd: (e) => {
                                                          e.preventDefault();
                                                          e.stopPropagation();
                                                          sendToBack(el.id);
                                                        }
                                                      } : {})}
                                                    >
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                                        <path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>
                                                      </svg>
                                                    </button>
                                                    <button
                                                      className="element-control-btn duplicate-btn"
                                                      title="Duplicate"
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        // احسب أعلى zIndex موجود + 1
                                                        const maxZIndex = elements.length > 0 ? Math.max(...elements.map(el => el.zIndex || 0)) : 0;
                                                        const newZIndex = maxZIndex + 1;

                                                        const newElement = {
                                                          ...JSON.parse(JSON.stringify(el)),
                                                          id: `${el.id}-copy-${Date.now()}`,
                                                          x: el.x + 20,
                                                          y: el.y + 20,
                                                          zIndex: newZIndex // إضافة zIndex جديد
                                                        };
                                                        setElements(prev => [...prev, newElement]);
                                                        setSelectedIds([newElement.id]);
                                                      }}
                                                      {...(isMobile ? {
                                                        onTouchStart: (e) => e.stopPropagation(),
                                                        onTouchEnd: (e) => {
                                                          e.preventDefault();
                                                          e.stopPropagation();
                                                          const maxZIndex = elements.length > 0 ? Math.max(...elements.map(el => el.zIndex || 0)) : 0;
                                                          const newZIndex = maxZIndex + 1;

                                                          const newElement = {
                                                            ...JSON.parse(JSON.stringify(el)),
                                                            id: `${el.id}-copy-${Date.now()}`,
                                                            x: el.x + 20,
                                                            y: el.y + 20,
                                                            zIndex: newZIndex
                                                          };
                                                          setElements(prev => [...prev, newElement]);
                                                          setSelectedIds([newElement.id]);
                                                        }
                                                      } : {})}
                                                    >
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                                                        <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                                                      </svg>
                                                    </button>
                                                    <button
                                                      className="element-control-btn delete-btn"
                                                      title="Delete"
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        const updatedElements = elements.filter(elem => elem.id !== el.id);
                                                        setElements(updatedElements);
                                                        setSelectedIds([]);
                                                        const elementToDelete = document.querySelector(`[data-element-id="${el.id}"]`);
                                                        if (elementToDelete) {
                                                          elementToDelete.style.transition = 'all 0.2s ease';
                                                          elementToDelete.style.transform = 'scale(0.8)';
                                                          elementToDelete.style.opacity = '0';
                                                        }
                                                      }}
                                                      {...(isMobile ? {
                                                        onTouchStart: (e) => e.stopPropagation(),
                                                        onTouchEnd: (e) => {
                                                          e.preventDefault();
                                                          e.stopPropagation();
                                                          const updatedElements = elements.filter(elem => elem.id !== el.id);
                                                          setElements(updatedElements);
                                                          setSelectedIds([]);
                                                          const elementToDelete = document.querySelector(`[data-element-id="${el.id}"]`);
                                                          if (elementToDelete) {
                                                            elementToDelete.style.transition = 'all 0.2s ease';
                                                            elementToDelete.style.transform = 'scale(0.8)';
                                                            elementToDelete.style.opacity = '0';
                                                          }
                                                        }
                                                      } : {})}
                                                    >
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                        <path fillRule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                      </svg>
                                                    </button>
                                                    {/* زر حفظ التصميم النصي */}
                                                    {(el.type === 'text' || el.type === 'label') && (
                                                      <button
                                                        className="element-control-btn save-style-btn"
                                                        title="Save as Text Style"
                                                        onClick={(e) => {
                                                          e.stopPropagation();
                                                          // جمع خصائص التصميم
                                                          const styleObj = {
                                                            fontFamily: el.fontFamily,
                                                            fontSize: el.fontSize,
                                                            fontWeight: el.fontWeight,
                                                            color: el.color,
                                                            backgroundColor: el.backgroundColor,
                                                            textAlign: el.textAlign,
                                                            lineHeight: el.lineHeight,
                                                            letterSpacing: el.letterSpacing,
                                                            textDecoration: el.textDecoration,
                                                            textTransform: el.textTransform,
                                                            fontStyle: el.fontStyle,
                                                            opacity: el.opacity,
                                                            textShadow: el.textShadow,
                                                            WebkitTextStroke: el.WebkitTextStroke,
                                                            transform: el.transform,
                                                            textEffect: el.textEffect,
                                                            textShadowColor: el.textShadowColor,
                                                            textShadowBlur: el.textShadowBlur,
                                                            textShadowOffset: el.textShadowOffset
                                                          };
                                                          saveTextStyle(styleObj);
                                                        }}
                                                      >
                                                        <FaRegStar size={18} />
                                                      </button>
                                                    )}
                                                  </div>

                                                  {/* Layer indicator */}
                                                  <div className="layer-indicator">
                                                    Layer {(() => {
                                                      // احسب ترتيب الطبقة بناءً على zIndex
                                                      const currentZIndex = el.zIndex || 0;
                                                      const higherLayers = elements.filter(element => (element.zIndex || 0) > currentZIndex).length;
                                                      return elements.length - higherLayers;
                                                    })()}
                                                  </div>

                                                  {/* Rotation Handle */}
                                                  <div
                                                    className="rotation-handle"
                                                    onMouseDown={(e) => handleRotationStart(e, el.id)}
                                                    {...(isMobile ? {
                                                      onTouchStart: (e) => { handleRotationStart(e, el.id); }
                                                    } : {})}
                                                  />

                                                  {/* Resize/Rotate Handles */}
                                                  {["top-left", "top-right", "bottom-left", "bottom-right"].map((corner) => (
                                                    <div
                                                      key={corner}
                                                      className={`resize-handle`}
                                                      style={{
                                                        cursor: ["top-left", "bottom-right"].includes(corner) ? "nwse-resize" : "nesw-resize",
                                                        ...getResizeHandlePosition(corner),
                                                      }}
                                                      onMouseDown={(e) => {
                                                        e.preventDefault();
                                                        setActiveResize({ elementId: el.id, corner });
                                                        handleMouseDown(e, el.id, corner);
                                                      }}
                                                      {...(isMobile ? {
                                                        onTouchStart: (e) => {
                                                          e.preventDefault();
                                                          setActiveResize({ elementId: el.id, corner });
                                                          handleMouseDown(e, el.id, corner);
                                                        },
                                                        onTouchEnd: handleTouchEndElement
                                                      } : {})}
                                                      onContextMenu={(e) => e.preventDefault()}
                                                      title="Drag to resize"
                                                    />
                                                  ))}
                                                </>
                                              )}
                                        </div>
                                    ))}
                                    <AlignmentContainer alignmentLines={alignmentLines} />
                                    {/* Crop Size Indicator أثناء السحب - داخل الكانفاس */}
                                    {(activeCrop.elementId || activeResize.elementId || activeTextFrameResize.elementId) && (() => {
                                        const el = elements.find(e => e.id === (activeCrop.elementId || activeResize.elementId || activeTextFrameResize.elementId));
                                        if (!el) return null;
                                        const indicatorWidth = 80;
                                        let left = el.x + (el.width / 2) - (indicatorWidth / 2);
                                        let top = el.y + el.height + 22;
                                        if (left + indicatorWidth > cardType?.width) {
                                            left = cardType?.width - indicatorWidth - 8;
                                        }
                                        if (left < 8) {
                                            left = 8;
                                        }
                                        if (top + 28 > cardType?.height) {
                                            top = cardType?.height - 28;
                                        }
                                        return (
                                            <div
                                                style={{
                                                    position: 'absolute',
                                                    left: left,
                                                    top: top,
                                                    background: 'rgba(0,0,0,0.85)',
                                                    color: '#fff',
                                                    borderRadius: 6,
                                                    padding: '2px 10px',
                                                    fontSize: 11,
                                                    fontWeight: 500,
                                                    boxShadow: '0 1px 4px rgba(0,0,0,0.13)',
                                                    zIndex: 2000,
                                                    pointerEvents: 'none',
                                                    userSelect: 'none',
                                                    minWidth: indicatorWidth,
                                                    textAlign: 'center',
                                                }}
                                            >
                                                {((el.type === 'text' || el.type === 'label') && (activeResize.elementId || activeTextFrameResize.elementId)) ? (
                                                    <>
                                                        {(activeResize.corner === 'left' || activeResize.corner === 'right' || activeTextFrameResize.side === 'left' || activeTextFrameResize.side === 'right') ? (
                                                            <>Width: {Math.round(el.width)} px</>
                                                        ) : (
                                                            <>Height: {Math.round(el.height)} px</>
                                                        )}
                                                    </>
                                                ) : (
                                                    <>W: {Math.round(el.width)} px | H: {Math.round(el.height)} px</>
                                                )}
                                            </div>
                                        );
                                    })()}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Mobile Sidebar - Bottom positioned for mobile */}
            {isMobile && <LeftSidebar isMobile={isMobile} />}

            {/* Mobile Zoom Controls */}
            {isMobile && (
                <div className="mobile-zoom-controls">
                    <motion.button
                        className="mobile-zoom-btn"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const newZoomLevel = Math.min(zoomLevel + 10, 200);
                            if (zoomLevel !== newZoomLevel) {
                                zoom(newZoomLevel);
                            }
                        }}
                        disabled={zoomLevel >= 200}
                        title="Zoom In"
                        style={{
                            opacity: zoomLevel >= 200 ? 0.5 : 1,
                            cursor: zoomLevel >= 200 ? 'not-allowed' : 'pointer'
                        }}
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="M21 21l-4.35-4.35"/>
                            <line x1="11" y1="8" x2="11" y2="14"/>
                            <line x1="8" y1="11" x2="14" y2="11"/>
                        </svg>
                    </motion.button>
                    <motion.button
                        className="mobile-zoom-btn"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const newZoomLevel = Math.max(zoomLevel - 10, 50);
                            if (zoomLevel !== newZoomLevel) {
                                zoom(newZoomLevel);
                            }
                        }}
                        disabled={zoomLevel <= 50}
                        title="Zoom Out"
                        style={{
                            opacity: zoomLevel <= 50 ? 0.5 : 1,
                            cursor: zoomLevel <= 50 ? 'not-allowed' : 'pointer'
                        }}
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="M21 21l-4.35-4.35"/>
                            <line x1="8" y1="11" x2="14" y2="11"/>
                        </svg>
                    </motion.button>
                    <div className="mobile-zoom-level">
                        {zoomLevel}%
                    </div>
                </div>
            )}

            {/* Mobile Gesture Hint */}
            {isMobile && selectedIds.length > 0 && showMobileHint && (
                <div className="mobile-gesture-hint">
                    <div className="mobile-gesture-hint-content">
                        <div className="mobile-gesture-hint-title">Touch Gestures</div>
                        <div className="mobile-gesture-hint-item">
                            <span className="mobile-gesture-icon">👆</span>
                            <span>Tap to select • Drag to move</span>
                        </div>
                        <div className="mobile-gesture-hint-item">
                            <span className="mobile-gesture-icon">🤏</span>
                            <span>Pinch to resize element</span>
                        </div>
                        <div className="mobile-gesture-hint-item">
                            <span className="mobile-gesture-icon">📐</span>
                            <span>Drag corners to resize</span>
                        </div>
                    </div>
                </div>
            )}

            {/* ColorPicker العائم بجانب منطقة التصميم مباشرة */}
            {colorPickerTargetId && (
                <div
                    className="color-picker-container"
                    style={{
                        position: 'fixed',
                        top: '50%',
                        left: isMobile ? '50%' : 'calc(50% + 400px)', // Center on mobile, beside design area on desktop
                        transform: isMobile ? 'translate(-50%, -50%)' : 'translateY(-50%)',
                        zIndex: 9999,
                        transition: 'all 0.3s ease',
                        pointerEvents: 'auto'
                    }}
                >
                    <ColorPicker
                        elementId={colorPickerTargetId}
                        open={true}
                        onClose={() => setColorPickerTargetId(null)}
                    />
                </div>
            )}


        </div>
    );
};

DesignSpace.propTypes = {
    updateTemplateData: PropTypes.func.isRequired,
    design: PropTypes.object.isRequired,
    onImageGenerationStart: PropTypes.func,
    onSaveAsDesign: PropTypes.func,
};

export default DesignSpace;