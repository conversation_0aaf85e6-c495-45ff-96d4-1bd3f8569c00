/* Mobile Design Space Styles */

/* Mobile bottom toolbar scrollbar styling */
.mobile-toolbar::-webkit-scrollbar {
  height: 4px;
}

.mobile-toolbar::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-toolbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.mobile-toolbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* Smooth scrolling for mobile toolbar */
.mobile-toolbar {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Mobile tool button animations */
.mobile-tool-button {
  transition: all 0.2s ease;
  backdrop-filter: none;
}

.mobile-tool-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mobile-tool-button.active {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.4);
}

/* Mobile content panel */
.mobile-content-panel {
  backdrop-filter: none;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  z-index: 9999 !important;
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
}

/* Mobile content panel drag handle */
.mobile-content-panel::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* Mobile canvas adjustments */
@media (max-width: 768px) {
  .design-space-container {
    padding: 8px !important;
    height: calc(100vh - 160px) !important; /* Account for top toolbar and bottom sidebar */
    overflow: auto;
  }

  .design-space {
    max-width: calc(100vw - 16px);
    max-height: calc(100vh - 200px);
    transform: scale(0.7); /* Slightly smaller for better mobile fit */
    transform-origin: center center;
    margin: auto;
  }

  /* Hide desktop controls on mobile */
  .desktop-only {
    display: none !important;
  }

  /* Adjust element controls for mobile */
  .element-controls {
    transform: scale(0.9);
    touch-action: manipulation;
  }

  .element-control-btn {
    width: 36px !important;
    height: 36px !important;
    font-size: 14px !important;
    touch-action: manipulation;
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.2s ease !important;
  }

  .element-control-btn:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  }

  .element-control-btn:active {
    transform: scale(0.95) !important;
  }

  /* Mobile-friendly resize handles */
  .resize-handle {
    width: 24px !important;
    height: 24px !important;
    border-width: 3px !important;
    touch-action: manipulation;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 2px solid #8b5cf6 !important;
    border-radius: 50% !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.2s ease !important;
  }

  .resize-handle:hover,
  .resize-handle:active {
    transform: scale(1.2) !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3) !important;
  }

  /* Mobile toolbar adjustments */
  .mobile-bottom-toolbar {
    height: 80px;
    padding: 8px 12px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(to top, rgba(17, 24, 39, 0.98), rgba(17, 24, 39, 0.95));
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Ensure mobile content doesn't overflow */
  .mobile-content-body {
    max-height: calc(70vh - 80px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 20px;
  }

  /* Ensure mobile panels are above everything */
  .mobile-content-panel {
    z-index: 9999 !important;
  }

  /* Mobile canvas adjustments */
  .mobile-canvas {
    margin-bottom: 100px;
  }

  /* Mobile-specific body adjustments */
  body.mobile-design-space {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }

  /* Prevent zoom on double tap and improve touch handling */
  .design-space * {
    touch-action: manipulation;
  }

  .design-space-container {
    touch-action: pan-x pan-y;
    overflow: hidden;
  }

  .draggable-element {
    touch-action: none;
  }

  .element-controls {
    touch-action: manipulation;
  }

  /* Mobile top toolbar improvements */
  .mobile-top-toolbar {
    position: sticky;
    top: 0;
    z-index: 100;
    background: rgba(249, 250, 251, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  /* Mobile save button styling */
  .mobile-save-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    border: none;
    color: white;
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  .mobile-save-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  }

  /* Mobile element toolbar positioning */
  .element-controls {
    position: absolute;
    z-index: 1001;
  }

  /* Mobile-friendly element selection */
  .element-selected-mobile {
    /* تم تعطيل هذا الكلاس ليتم الاعتماد فقط على .selected */
    /* box-shadow: 0 0 0 2px #8b5cf6, 0 0 20px rgba(139, 92, 246, 0.3); */
    /* border-radius: 4px; */
  }
  
  /* Mobile-specific animations */
  .mobile-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  /* Touch-friendly spacing */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Mobile canvas zoom controls */
  .mobile-zoom-controls {
    position: fixed;
    bottom: 100px;
    right: 16px;
    z-index: 40;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }

  .mobile-zoom-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: none;
    border: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    touch-action: manipulation;
  }

  .mobile-zoom-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .mobile-zoom-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .mobile-zoom-level {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    min-width: 44px;
    text-align: center;
  }
  
  /* Mobile gesture indicators */
  .mobile-gesture-hint {
    position: fixed;
    bottom: 180px;
    left: 16px;
    right: 16px;
    background: rgba(17, 24, 39, 0.95);
    color: white;
    border-radius: 12px;
    font-size: 13px;
    z-index: 1000;
    pointer-events: none;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    animation: slideUpFade 0.3s ease-out;
  }

  .mobile-gesture-hint-content {
    padding: 12px 16px;
  }

  .mobile-gesture-hint-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 8px;
    color: #f3f4f6;
    text-align: center;
  }

  .mobile-gesture-hint-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 12px;
    color: #d1d5db;
  }

  .mobile-gesture-hint-item:last-child {
    margin-bottom: 0;
  }

  .mobile-gesture-icon {
    font-size: 16px;
    margin-right: 8px;
    width: 20px;
    text-align: center;
  }

  @keyframes slideUpFade {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  /* Mobile-specific element selection */
  .mobile-element-selected {
    /* تم تعطيل هذا الكلاس ليتم الاعتماد فقط على .selected */
    /* outline: 2px solid #8b5cf6; */
    /* outline-offset: 2px; */
  }

  /* Pinch gesture feedback */
  .element-pinching {
    transform-origin: center center !important;
    transition: none !important;
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.4) !important;
  }

  /* Resize gesture feedback */
  .element-resizing {
    transition: none !important;
    box-shadow: 0 0 15px rgba(34, 197, 94, 0.4) !important;
  }

  /* Touch feedback for draggable elements */
  .draggable-element:active {
    transform: scale(1.02) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  }
  
  /* Mobile toolbar backdrop */
  .mobile-toolbar-backdrop {
    background: linear-gradient(
      to top,
      rgba(17, 24, 39, 0.95) 0%,
      rgba(17, 24, 39, 0.8) 50%,
      transparent 100%
    );
    backdrop-filter: none;
  }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .design-space {
    transform: scale(0.5); /* Smaller scale for landscape */
  }

  .design-space-container {
    height: calc(100vh - 120px) !important;
  }

  .mobile-content-panel {
    height: 60vh !important;
    max-height: 350px !important;
  }

  .mobile-bottom-toolbar {
    height: 60px;
    padding: 4px 8px;
  }

  .mobile-tool-button {
    padding: 6px !important;
    min-width: 45px !important;
  }

  .mobile-tool-button span {
    font-size: 9px !important;
  }

  .mobile-zoom-controls {
    bottom: 70px;
    right: 12px;
  }

  .mobile-zoom-btn {
    width: 36px;
    height: 36px;
  }

  .mobile-zoom-level {
    font-size: 10px;
    padding: 2px 6px;
    min-width: 36px;
  }
}

/* High DPI mobile displays */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  .design-space {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  .element-controls {
    image-rendering: -webkit-optimize-contrast;
  }

  /* Sharper icons and text on high DPI */
  .mobile-tool-button svg,
  .mobile-zoom-btn svg {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Mobile performance optimizations */
@media (max-width: 768px) {
  /* GPU acceleration for smooth animations */
  .mobile-tool-button,
  .mobile-content-panel,
  .mobile-zoom-btn,
  .design-space {
    will-change: transform;
    transform: translateZ(0);
  }

  /* Optimize scrolling performance */
  .mobile-content-body,
  .mobile-toolbar {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }

  /* Reduce repaints during interactions */
  .element-controls,
  .resize-handle {
    will-change: transform, opacity;
  }
}

/* Dark mode mobile adjustments */
@media (max-width: 768px) and (prefers-color-scheme: dark) {
  .mobile-content-panel {
    background: rgba(17, 24, 39, 0.95);
    color: white;
  }
  
  .mobile-zoom-btn {
    background: rgba(17, 24, 39, 0.9);
    color: white;
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Better touch targets */
  .mobile-tool-button {
    min-height: 48px;
    min-width: 48px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .mobile-tool-button.active {
    background: rgba(139, 92, 246, 0.9);
    border-color: rgba(139, 92, 246, 1);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
  }

  /* Mobile toolbar improvements */
  .mobile-toolbar {
    gap: 12px;
    padding: 0 8px;
  }

  /* Mobile content panel improvements */
  .mobile-content-panel {
    border-radius: 20px 20px 0 0;
    box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.3);
  }

  /* Mobile gesture area */
  .mobile-content-panel::before {
    width: 50px;
    height: 5px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    top: 12px;
  }

  /* Mobile safe area adjustments */
  .mobile-bottom-toolbar {
    padding-bottom: max(8px, env(safe-area-inset-bottom));
  }

  /* Mobile element selection improvements */
  .mobile-element-selected {
    outline: 3px solid #8b5cf6;
    outline-offset: 3px;
    border-radius: 4px;
  }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
  .mobile-tool-button:focus {
    outline: 3px solid #8b5cf6;
    outline-offset: 2px;
  }

  .mobile-content-panel:focus-within {
    box-shadow: 0 0 0 3px #8b5cf6;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .mobile-tool-button,
    .mobile-content-panel,
    .mobile-slide-up {
      animation: none !important;
      transition: none !important;
    }
  }
}

@media (max-width: 768px) {
  .selected {
    border: 2px solid var(--canva-primary) !important;
    box-shadow: 0 0 0 1px var(--canva-primary), 0 0 8px rgba(139, 61, 255, 0.3);
  }
}
